# ThyView API Specification

This document provides detailed specifications for all API endpoints in the ThyView AWS backend.

## Authentication

All endpoints require authentication unless explicitly marked as public.

**Authentication Header:**
```
Authorization: Bearer {jwt_token}
```

## Posts

### Create Post
- **Endpoint:** `POST /posts`
- **Handler:** `src/reviews/posts/createPost.handler`
- **Description:** Creates a new post with optional image
- **Request:**
  ```json
  {
    "title": "Post Title",
    "content": "Post content text",
    "imageUrl": "https://example.com/image.jpg" // Optional
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "postId": "post-uuid",
    "message": "Post created successfully"
  }
  ```

### Get Posts List
- **Endpoint:** `GET /posts`
- **Handler:** `src/reviews/posts/getPostsList.getPostsList`
- **Description:** Retrieves a paginated list of posts
- **Query Parameters:**
  - `limit` (optional): Number of posts to retrieve (default: 10)
  - `lastEvaluatedKey` (optional): Key for pagination
- **Response:**
  ```json
  {
    "posts": [
      {
        "id": "post-id",
        "title": "Post Title",
        "content": "Post content text",
        "imageUrl": "https://example.com/image.jpg",
        "authorUid": "user-id",
        "authorName": "John Doe",
        "createdAt": "2023-01-01T12:00:00Z",
        "updatedAt": "2023-01-01T12:00:00Z",
        "likes": 42
      }
    ],
    "lastEvaluatedKey": "key-for-next-page"
  }
  ```

### Edit Post
- **Endpoint:** `PUT /posts/{postId}`
- **Handler:** `src/reviews/posts/editPost.editPost`
- **Description:** Updates an existing post
- **Request:**
  ```json
  {
    "title": "Updated Title",
    "content": "Updated content text",
    "imageUrl": "https://example.com/new-image.jpg" // Optional
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "message": "Post updated successfully"
  }
  ```

### Delete Post
- **Endpoint:** `DELETE /posts/{postId}`
- **Handler:** `src/reviews/posts/deletePost.deletePost`
- **Description:** Deletes a post and its associated comments
- **Response:**
  ```json
  {
    "success": true,
    "message": "Post deleted successfully"
  }
  ```

### Like Post
- **Endpoint:** `POST /posts/{postId}/like`
- **Handler:** `src/reviews/posts/likePost.likePost`
- **Description:** Toggles like status on a post
- **Response:**
  ```json
  {
    "success": true,
    "liked": true, // or false if unliked
    "likes": 43 // Updated like count
  }
  ```

### Has User Liked
- **Endpoint:** `GET /posts/{postId}/hasLiked`
- **Handler:** `src/reviews/posts/hasUserLiked.hasUserLiked`
- **Description:** Checks if the current user has liked a specific post
- **Response:**
  ```json
  {
    "hasLiked": true // or false
  }
  ```

### Get Post Detail
- **Endpoint:** `GET /posts/{postId}`
- **Handler:** `src/reviews/posts/getPostDetail.getPostDetail`
- **Description:** Gets detailed information about a specific post
- **Response:**
  ```json
  {
    "id": "post-id",
    "title": "Post Title",
    "content": "Post content text",
    "imageUrl": "https://example.com/image.jpg",
    "authorUid": "user-id",
    "authorName": "John Doe",
    "authorProfilePic": "https://example.com/profile.jpg",
    "createdAt": "2023-01-01T12:00:00Z",
    "updatedAt": "2023-01-01T12:00:00Z",
    "likes": 42,
    "comments": 7
  }
  ```

### Get Image Upload URL
- **Endpoint:** `POST /posts/uploadUrl`
- **Handler:** `src/reviews/posts/getImageUploadUrl.getImageUploadUrl`
- **Description:** Generates a pre-signed URL for uploading an image to S3
- **Request:**
  ```json
  {
    "fileName": "image.jpg",
    "contentType": "image/jpeg"
  }
  ```
- **Response:**
  ```json
  {
    "uploadUrl": "https://presigned-s3-url-for-upload",
    "imageUrl": "https://final-image-url-after-upload"
  }
  ```

## Comments

### Add Comment
- **Endpoint:** `POST /comments`
- **Handler:** `src/reviews/comments/addComment.addComment`
- **Description:** Adds a new comment to a post or as a reply to another comment
- **Request:**
  ```json
  {
    "postId": "post-id",
    "parentCommentId": "comment-id", // Optional, for replies
    "content": "Comment text"
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "commentId": "new-comment-id",
    "message": "Comment added successfully"
  }
  ```

### Get Comments
- **Endpoint:** `GET /posts/{postId}/comments`
- **Handler:** `src/reviews/comments/getComment.getComments`
- **Description:** Gets comments for a specific post
- **Query Parameters:**
  - `limit` (optional): Number of comments to retrieve (default: 20)
  - `lastEvaluatedKey` (optional): Key for pagination
- **Response:**
  ```json
  {
    "comments": [
      {
        "id": "comment-id",
        "postId": "post-id",
        "parentCommentId": null,
        "content": "Comment text",
        "authorUid": "user-id",
        "authorName": "Jane Doe",
        "authorProfilePic": "https://example.com/profile.jpg",
        "createdAt": "2023-01-01T12:30:00Z",
        "likes": 5,
        "replies": 2
      }
    ],
    "lastEvaluatedKey": "key-for-next-page"
  }
  ```

### Get Replies
- **Endpoint:** `GET /comments/{commentId}/replies`
- **Handler:** `src/reviews/comments/getReplies.getReplies`
- **Description:** Gets replies to a specific comment
- **Query Parameters:**
  - `limit` (optional): Number of replies to retrieve (default: 10)
  - `lastEvaluatedKey` (optional): Key for pagination
- **Response:**
  ```json
  {
    "replies": [
      {
        "id": "reply-id",
        "postId": "post-id",
        "parentCommentId": "comment-id",
        "content": "Reply text",
        "authorUid": "user-id",
        "authorName": "Bob Smith",
        "authorProfilePic": "https://example.com/profile.jpg",
        "createdAt": "2023-01-01T13:00:00Z",
        "likes": 1
      }
    ],
    "lastEvaluatedKey": "key-for-next-page"
  }
  ```

### Like Comment
- **Endpoint:** `POST /comments/{commentId}/like`
- **Handler:** `src/reviews/comments/likeComment.likeComment`
- **Description:** Toggles like status on a comment
- **Response:**
  ```json
  {
    "success": true,
    "liked": true, // or false if unliked
    "likes": 6 // Updated like count
  }
  ```

### Delete Comment
- **Endpoint:** `DELETE /comments/{commentId}`
- **Handler:** `src/reviews/comments/deleteComment.deleteComment`
- **Description:** Deletes a comment and its replies
- **Response:**
  ```json
  {
    "success": true,
    "message": "Comment deleted successfully"
  }
  ```

## Reports

### Report Post
- **Endpoint:** `POST /posts/{postId}/report`
- **Handler:** `src/reviews/reports/reportPost.reportPost`
- **Description:** Reports a post for moderation
- **Request:**
  ```json
  {
    "reason": "spam", // Options: spam, offensive, inappropriate, other
    "details": "Additional details about the report" // Optional
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "message": "Post reported successfully"
  }
  ```

### Report Comment
- **Endpoint:** `POST /comments/{commentId}/report`
- **Handler:** `src/reviews/reports/reportComment.reportComment`
- **Description:** Reports a comment for moderation
- **Request:**
  ```json
  {
    "reason": "offensive", // Options: spam, offensive, inappropriate, other
    "details": "Additional details about the report" // Optional
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "message": "Comment reported successfully"
  }
  ```

## Logging

### App to Cloud Logger
- **Endpoint:** `POST /log`
- **Handler:** `src/utils/cloud-logger.appToCloudLogger`
- **Description:** Sends client logs to CloudWatch
- **Request:**
  ```json
  {
    "level": "error", // Options: debug, info, warn, error
    "message": "Error message",
    "context": {
      "screen": "HomeScreen",
      "action": "loadPosts",
      "timestamp": "2023-01-01T12:00:00Z"
    }
  }
  ```
- **Response:**
  ```json
  {
    "success": true
  }
  ```