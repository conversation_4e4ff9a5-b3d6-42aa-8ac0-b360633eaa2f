{
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2020,
    "sourceType": "module",
    "project": "./tsconfig.json",
    "tsconfigRootDir": "."
  },
  "env": {
    "es6": true,
    "node": true,
    "jest": true
  },
  "plugins": [
    "@typescript-eslint",
    "import",
    "promise"
  ],
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:import/errors",
    "plugin:import/warnings",
    "plugin:import/typescript"
  ],
  "ignorePatterns": [
    "node_modules",
    "dist",
    "lib",
    "coverage",
    ".webpack"
  ],
  "rules": {
    // Basic rules
    "quotes": ["error", "double"],
    "no-console": "warn",
    "no-debugger": "warn",
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": ["error", { "argsIgnorePattern": "^_" }],

    // Error prevention
    "no-template-curly-in-string": "warn",
    "eqeqeq": ["error", "always"],
    "no-alert": "error",
    "no-eval": "error",
    "no-extend-native": "warn",

    // Promises
    "promise/always-return": "error",
    "promise/catch-or-return": "error",
    "promise/no-nesting": "warn",
    "prefer-promise-reject-errors": "error",

    // Code clarity
    "array-callback-return": "warn",
    "consistent-return": "warn",
    
    // Import organization
    "import/order": [
      "error",
      {
        "groups": ["builtin", "external", "internal", "parent", "sibling", "index"],
        "newlines-between": "always",
        "alphabetize": { "order": "asc", "caseInsensitive": true }
      }
    ]
  },
  "settings": {
    "import/resolver": {
      "node": {
        "extensions": [".js", ".ts"],
        "moduleDirectory": ["node_modules", "src/"]
      },
      "typescript": {
        "alwaysTryTypes": true
      }
    }
  }
}
