{"compilerOptions": {"target": "es2022", "module": "node16", "outDir": "dist", "rootDir": ".", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "sourceMap": true, "noImplicitReturns": true, "moduleResolution": "node16", "lib": ["es2015", "es2016", "es2017", "es2018", "es2019", "es2022", "dom"], "allowJs": true, "baseUrl": ".", "useDefineForClassFields": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "paths": {"@src/*": ["src/*"], "@common/*": ["src/common/*"], "@utils/*": ["src/common/utils/*"], "@models/*": ["src/common/models/*"], "@config/*": ["src/common/config/*"], "@services/*": ["src/services/*"], "@types/*": ["src/common/types/*"]}}, "include": ["src/**/*", "tests/**/*", "node_modules/dynamoose/dist/**/*.d.ts"], "exclude": ["node_modules/**/*", "dist", ".webpack", ".build", ".serverless"]}