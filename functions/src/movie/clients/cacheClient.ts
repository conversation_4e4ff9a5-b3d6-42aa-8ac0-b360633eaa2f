import Redis from 'ioredis';
import { Logger } from '../../common/utils/logger';
import { TrendingItem } from '../types';

export class CacheClient {
  private redis: Redis;
  private logger: Logger;

  constructor() {
    this.logger = Logger.getInstance('cache-client');

    const redisUrl = process.env.REDIS_URL;

    if (!redisUrl) {
      throw new Error('REDIS_URL is not defined in environment variables');
    }

    this.logger.debug('Initializing Redis client with URL', {
      redisUrl: redisUrl.slice(0, 20) + '...' // Mask sensitive info in logs
    });

    this.redis = new Redis(redisUrl, {
      tls: {}  // Ensures secure TLS connection for Upstash
    });

    this.redis.on('connect', () => {
      this.logger.info('Redis client connected');
    });

    this.redis.on('error', (error) => {
      this.logger.error('Redis client error', error);
    });

    this.redis.on('close', () => {
      this.logger.warn('Redis client connection closed');
    });

    this.redis.on('reconnecting', () => {
      this.logger.info('Redis client reconnecting...');
    });
  }

  async get(key: string): Promise<any> {
    const start = Date.now();
    this.logger.debug('Cache get', { key });

    try {
      const result = await this.redis.get(key);
      const executionTime = Date.now() - start;

      if (result) {
        this.logger.debug('Cache hit', {
          key,
          executionTime: `${executionTime}ms`,
          size: result.length
        });
        return JSON.parse(result);
      } else {
        this.logger.debug('Cache miss', {
          key,
          executionTime: `${executionTime}ms`
        });
        return null;
      }
    } catch (error) {
      this.logger.error('Cache get error', error, {
        key,
        executionTime: `${Date.now() - start}ms`
      });
      return null;
    }
  }

  async set(key: string, value: any, ttlSeconds: number = 600): Promise<boolean> {
    const start = Date.now();
    this.logger.debug('Cache set', { key, ttlSeconds });

    try {
      const serialized = JSON.stringify(value);
      await this.redis.setex(key, ttlSeconds, serialized);

      this.logger.debug('Cache set successful', {
        key,
        ttlSeconds,
        executionTime: `${Date.now() - start}ms`,
        size: serialized.length
      });
      return true;
    } catch (error) {
      this.logger.error('Cache set error', error, {
        key,
        ttlSeconds,
        executionTime: `${Date.now() - start}ms`
      });
      return false;
    }
  }

  async incrementTrending(query: string, ttlSeconds: number = 604800): Promise<boolean> {
    const key = `trending:${query}`;
    this.logger.debug('Incrementing trending counter', { key });

    try {
      const pipeline = this.redis.pipeline();
      pipeline.incr(key);
      pipeline.expire(key, ttlSeconds);
      await pipeline.exec();

      this.logger.debug('Trending counter incremented', {
        key,
        ttlSeconds
      });
      return true;
    } catch (error) {
      this.logger.error('Failed to increment trending counter', error, {
        key,
        ttlSeconds
      });
      return false;
    }
  }

  async getTrending(limit: number = 10): Promise<TrendingItem[]> {
    this.logger.debug('Fetching trending items', { limit });
    const start = Date.now();

    try {
      const keys = await this.redis.keys('trending:*');
      this.logger.debug('Found trending keys', { count: keys.length });

      if (keys.length === 0) {
        this.logger.debug('No trending items found');
        return [];
      }

      const pipeline = this.redis.pipeline();
      keys.forEach(key => pipeline.get(key));
      const results = await pipeline.exec();

      const trending: TrendingItem[] = [];
      keys.forEach((key, index) => {
        const count = results?.[index]?.[1] as string;
        if (count) {
          trending.push({
            query: key.replace('trending:', ''),
            count: parseInt(count)
          });
        }
      });

      const sorted = trending.sort((a, b) => b.count - a.count).slice(0, limit);

      this.logger.debug('Returning trending items', {
        count: sorted.length,
        executionTime: `${Date.now() - start}ms`
      });

      return sorted;
    } catch (error) {
      this.logger.error('Failed to get trending items', error, {
        limit,
        executionTime: `${Date.now() - start}ms`
      });
      return [];
    }
  }
}
