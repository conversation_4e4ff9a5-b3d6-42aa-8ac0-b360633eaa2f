// src/clients/tmdbClient.ts
const fetch = (...args: any[]) => import('node-fetch').then(({default: fetch}) => fetch(...args as [any]));
import { Logger } from '../../common/utils/logger';

export class TMDBClient {
  private logger: Logger;
  private apiKey: string;
  private baseUrl = 'https://api.themoviedb.org/3';

  constructor() {
    this.logger = Logger.getInstance('tmdb-client');
    this.apiKey = process.env.TMDB_API_KEY!;
    
    if (!this.apiKey) {
      this.logger.error('TMDB_API_KEY is not set in environment variables');
      throw new Error('TMDB_API_KEY is required');
    }
    
    this.logger.info('TMDBClient initialized');
  }

  async searchMovie(query: string) {
    const url = `${this.baseUrl}/search/movie?api_key=${this.apiKey}&query=${encodeURIComponent(query)}`;
    this.logger.info('Searching TMDB for movie', { query, url: url.replace(this.apiKey, '***') });
    
    try {
      const response = await fetch(url);
      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error('TMDB API error', new Error(`Status: ${response.status}`), { 
          query, 
          status: response.status,
          statusText: response.statusText,
          error: errorText.substring(0, 200) // Log first 200 chars to avoid huge logs
        });
        throw new Error(`TMDB API error: ${response.status} ${response.statusText}`);
      }
      
      const data: any = await response.json();
      this.logger.info('Received TMDB movie results', { 
        query, 
        resultCount: data.results?.length || 0 
      });
      return data;
    } catch (error) {
      this.logger.error('Failed to search TMDB for movie', error, { query });
      throw error;
    }
  }

  async searchTV(query: string) {
    const url = `${this.baseUrl}/search/tv?api_key=${this.apiKey}&query=${encodeURIComponent(query)}`;
    this.logger.info('Searching TMDB for TV show', { query, url: url.replace(this.apiKey, '***') });
    
    try {
      const response = await fetch(url);
      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error('TMDB TV search error', new Error(`Status: ${response.status}`), { 
          query,
          status: response.status,
          statusText: response.statusText
        });
        throw new Error(`TMDB API error: ${response.status} ${response.statusText}`);
      }
      
      const data: any = await response.json();
      this.logger.info('Received TMDB TV results', { 
        query, 
        resultCount: data.results?.length || 0 
      });
      return data;
    } catch (error) {
      this.logger.error('Failed to search TMDB for TV show', error, { query });
      throw error;
    }
  }

  async searchPerson(query: string) {
    const url = `${this.baseUrl}/search/person?api_key=${this.apiKey}&query=${encodeURIComponent(query)}`;
    this.logger.info('Searching TMDB for person', { query, url: url.replace(this.apiKey, '***') });
    
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`TMDB API error: ${response.status} ${response.statusText}`);
      }
      
      const data: any = await response.json();
      this.logger.info('Received TMDB person results', { 
        query, 
        resultCount: data.results?.length || 0 
      });
      return data;
    } catch (error) {
      this.logger.error('Failed to search TMDB for person', error, { query });
      throw error;
    }
  }

  async searchMulti(query: string) {
    const url = `${this.baseUrl}/search/multi?api_key=${this.apiKey}&query=${encodeURIComponent(query)}`;
    this.logger.info('Searching TMDB multi', { query, url: url.replace(this.apiKey, '***') });
    
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`TMDB API error: ${response.status} ${response.statusText}`);
      }
      
      const data: any = await response.json();
      this.logger.info('Received TMDB multi results', { 
        query, 
        resultCount: data.results?.length || 0 
      });
      return data;
    } catch (error) {
      this.logger.error('Failed to perform multi search on TMDB', error, { query });
      throw error;
    }
  }

  async discoverByGenre(genreId: number, type: 'movie' | 'tv' = 'movie') {
    const endpoint = type === 'movie' ? 'discover/movie' : 'discover/tv';
    const url = `${this.baseUrl}/${endpoint}?api_key=${this.apiKey}&with_genres=${genreId}`;
    this.logger.info('Discovering by genre', { genreId, type, url: url.replace(this.apiKey, '***') });
    
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`TMDB API error: ${response.status} ${response.statusText}`);
      }
      
      const data: any = await response.json();
      this.logger.info('Received TMDB genre results', { 
        genreId, 
        type,
        resultCount: data.results?.length || 0 
      });
      return data;
    } catch (error) {
      this.logger.error('Failed to discover by genre', error, { genreId, type });
      throw error;
    }
  }

  async getMovieDetails(id: number) {
    const url = `${this.baseUrl}/movie/${id}?api_key=${this.apiKey}&append_to_response=credits,similar`;
    this.logger.debug('Fetching movie details', { movieId: id, url: url.replace(this.apiKey, '***') });
    
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`TMDB API error: ${response.status} ${response.statusText}`);
      }
      
      const data: any = await response.json();
      this.logger.debug('Received movie details', { 
        movieId: id,
        title: data.title
      });
      return data;
    } catch (error) {
      this.logger.error('Failed to fetch movie details', error, { movieId: id });
      throw error;
    }
  }

  async getTVDetails(id: number) {
    const url = `${this.baseUrl}/tv/${id}?api_key=${this.apiKey}&append_to_response=credits,similar`;
    this.logger.info('Fetching TV show details', { tvId: id, url: url.replace(this.apiKey, '***') });
    
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`TMDB API error: ${response.status} ${response.statusText}`);
      }
      
      const data: any = await response.json();
      this.logger.info('Received TV show details', { 
        tvId: id,
        name: data.name
      });
      return data;
    } catch (error) {
      this.logger.error('Failed to fetch TV show details', error, { tvId: id });
      throw error;
    }
  }

  async getPersonDetails(id: number) {
    const url = `${this.baseUrl}/person/${id}?api_key=${this.apiKey}&append_to_response=movie_credits,tv_credits`;
    this.logger.info('Fetching person details', { personId: id, url: url.replace(this.apiKey, '***') });
    
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`TMDB API error: ${response.status} ${response.statusText}`);
      }
      
      const data: any = await response.json();
      this.logger.info('Received person details', { 
        personId: id,
        name: data.name
      });
      return data;
    } catch (error) {
      this.logger.error('Failed to fetch person details', error, { personId: id });
      throw error;
    }
  }
}
