// src/types/index.ts
// src/types/index.ts

export interface SearchResult {
  id: number;
  type: 'movie' | 'tv' | 'person';
  
  // Movie/TV common fields
  title?: string;
  name?: string;
  release_year?: number;
  poster?: string;
  overview?: string;
  rating?: number;
  genres?: string[];
  
  // Movie specific
  runtime?: number;
  director?: string;
  
  // TV specific
  seasons?: number;
  episodes?: number;
  status?: string;
  creators?: string[];
  
  // Person specific
  profile?: string;
  known_for_department?: string;
  biography?: string;
  birthday?: string;
  place_of_birth?: string;
  known_for?: Array<{
    id: number;
    title: string;
    type: string;
  }> | string[]; // Support both enriched and basic format
  
  // Cast for movies/TV
  cast?: Array<{
    id: number;
    name: string;
    character: string;
    profile_path?: string;
  }>;
}

export interface SearchResponse {
  query: string;
  type: string;
  results: SearchResult[];
}
export interface TrendingItem {
  query: string;
  count: number;
}

export interface SearchLog {
  query: string;
  label: string;
  timestamp: number;
  userId?: string;
  source: 'cache' | 'api';
}