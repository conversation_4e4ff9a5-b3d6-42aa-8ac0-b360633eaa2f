// src/services/detailsService.ts
import { TMDBClient } from '../clients/tmdbClient';
import { CacheClient } from '../clients/cacheClient';
import { Logger } from '../../common/utils/logger';

export class DetailsService {
  private tmdb: TMDBClient;
  private cache: CacheClient;
  private logger: Logger;

  constructor() {
    this.logger = Logger.getInstance('details-service');
    this.tmdb = new TMDBClient();
    this.cache = new CacheClient();
    this.logger.info('DetailsService initialized');
  }

  async getMovieDetails(id: number) {
    this.logger.debug('Getting movie details', { id });
    const cacheKey = `details:movie:${id}`;

    const cached = await this.cache.get(cacheKey);
    if (cached) {
      this.logger.info('Cache hit for movie details', { id });
      return cached;
    }

    try {
      const details = await this.tmdb.getMovieDetails(id);
      const enriched = await this.enrichMovieDetails(details);

      await this.cache.set(cacheKey, enriched, 3600);
      this.logger.info('Movie details cached', { id });
      return enriched;
    } catch (error) {
      this.logger.error('Failed to get movie details', error, { id });
      throw error;
    }
  }

  async getTVDetails(id: number) {
    this.logger.debug('Getting TV details', { id });
    const cacheKey = `details:tv:${id}`;

    const cached = await this.cache.get(cacheKey);
    if (cached) {
      this.logger.info('Cache hit for TV details', { id });
      return cached;
    }

    try {
      const details = await this.tmdb.getTVDetails(id);
      const enriched = await this.enrichTVDetails(details);

      await this.cache.set(cacheKey, enriched, 3600);
      this.logger.info('TV details cached', { id });
      return enriched;
    } catch (error) {
      this.logger.error('Failed to get TV details', error, { id });
      throw error;
    }
  }

  async getPersonDetails(id: number) {
    this.logger.debug('Getting person details', { id });
    const cacheKey = `details:person:${id}`;

    const cached = await this.cache.get(cacheKey);
    if (cached) {
      this.logger.info('Cache hit for person details', { id });
      return cached;
    }

    try {
      const details = await this.tmdb.getPersonDetails(id);
      const enriched = await this.enrichPersonDetails(details);

      await this.cache.set(cacheKey, enriched, 3600);
      this.logger.info('Person details cached', { id });
      return enriched;
    } catch (error) {
      this.logger.error('Failed to get person details', error, { id });
      throw error;
    }
  }

  private async enrichMovieDetails(details: any) {
    this.logger.debug('Enriching movie details', { id: details.id });
    
    return {
      id: details.id,
      title: details.title,
      original_title: details.original_title,
      tagline: details.tagline,
      year: details.release_date ? new Date(details.release_date).getFullYear() : null,
      release_date: details.release_date,
      rating: details.vote_average,
      vote_count: details.vote_count,
      popularity: details.popularity,
      poster: details.poster_path ? `https://image.tmdb.org/t/p/w500${details.poster_path}` : null,
      backdrop: details.backdrop_path ? `https://image.tmdb.org/t/p/original${details.backdrop_path}` : null,
      overview: details.overview,
      runtime: details.runtime,
      budget: details.budget,
      revenue: details.revenue,
      status: details.status,
      adult: details.adult,
      original_language: details.original_language,
      spoken_languages: details.spoken_languages?.map((lang: any) => ({
        code: lang.iso_639_1,
        name: lang.english_name || lang.name
      })) || [],
      genres: details.genres?.map((g: any) => ({
        id: g.id,
        name: g.name
      })) || [],
      production_companies: details.production_companies?.map((company: any) => ({
        id: company.id,
        name: company.name,
        logo: company.logo_path ? `https://image.tmdb.org/t/p/w500${company.logo_path}` : null,
        origin_country: company.origin_country
      })) || [],
      production_countries: details.production_countries?.map((country: any) => ({
        code: country.iso_3166_1,
        name: country.name
      })) || [],
      
      // Enhanced cast and crew
      cast: details.credits?.cast?.slice(0, 15).map((c: any) => ({
        id: c.id,
        name: c.name,
        character: c.character,
        credit_id: c.credit_id,
        order: c.order,
        profile: c.profile_path ? `https://image.tmdb.org/t/p/w500${c.profile_path}` : null,
        known_for_department: c.known_for_department,
        popularity: c.popularity
      })) || [],
      
      crew: details.credits?.crew?.map((c: any) => ({
        id: c.id,
        name: c.name,
        job: c.job,
        department: c.department,
        credit_id: c.credit_id,
        profile: c.profile_path ? `https://image.tmdb.org/t/p/w500${c.profile_path}` : null,
        known_for_department: c.known_for_department,
        popularity: c.popularity
      })) || [],
      
      // Key crew members
      director: details.credits?.crew?.find((c: any) => c.job === 'Director')?.name,
      directors: details.credits?.crew?.filter((c: any) => c.job === 'Director').map((c: any) => ({
        id: c.id,
        name: c.name,
        profile: c.profile_path ? `https://image.tmdb.org/t/p/w500${c.profile_path}` : null
      })) || [],
      
      writers: details.credits?.crew?.filter((c: any) => 
        ['Writer', 'Screenplay', 'Story'].includes(c.job)
      ).slice(0, 5).map((c: any) => ({
        id: c.id,
        name: c.name,
        job: c.job,
        profile: c.profile_path ? `https://image.tmdb.org/t/p/w500${c.profile_path}` : null
      })) || [],
      
      producers: details.credits?.crew?.filter((c: any) => 
        ['Producer', 'Executive Producer'].includes(c.job)
      ).slice(0, 5).map((c: any) => ({
        id: c.id,
        name: c.name,
        job: c.job,
        profile: c.profile_path ? `https://image.tmdb.org/t/p/w500${c.profile_path}` : null
      })) || [],
      
      // Enhanced similar movies
      similar: details.similar?.results?.slice(0, 10).map((s: any) => ({
        id: s.id,
        title: s.title,
        year: s.release_date ? new Date(s.release_date).getFullYear() : null,
        poster: s.poster_path ? `https://image.tmdb.org/t/p/w500${s.poster_path}` : null,
        rating: s.vote_average,
        overview: s.overview
      })) || [],
      
      // Recommendations
      recommendations: details.recommendations?.results?.slice(0, 10).map((r: any) => ({
        id: r.id,
        title: r.title,
        year: r.release_date ? new Date(r.release_date).getFullYear() : null,
        poster: r.poster_path ? `https://image.tmdb.org/t/p/w500${r.poster_path}` : null,
        rating: r.vote_average,
        overview: r.overview
      })) || [],
      
      // Additional metadata
      imdb_id: details.imdb_id,
      homepage: details.homepage,
      belongs_to_collection: details.belongs_to_collection ? {
        id: details.belongs_to_collection.id,
        name: details.belongs_to_collection.name,
        poster: details.belongs_to_collection.poster_path ? 
          `https://image.tmdb.org/t/p/w500${details.belongs_to_collection.poster_path}` : null,
        backdrop: details.belongs_to_collection.backdrop_path ? 
          `https://image.tmdb.org/t/p/original${details.belongs_to_collection.backdrop_path}` : null
      } : null
    };
  }

  private async enrichTVDetails(details: any) {
    this.logger.debug('Enriching TV details', { id: details.id });
    
    return {
      id: details.id,
      name: details.name,
      original_name: details.original_name,
      tagline: details.tagline,
      year: details.first_air_date ? new Date(details.first_air_date).getFullYear() : null,
      first_air_date: details.first_air_date,
      last_air_date: details.last_air_date,
      rating: details.vote_average,
      vote_count: details.vote_count,
      popularity: details.popularity,
      poster: details.poster_path ? `https://image.tmdb.org/t/p/w500${details.poster_path}` : null,
      backdrop: details.backdrop_path ? `https://image.tmdb.org/t/p/original${details.backdrop_path}` : null,
      overview: details.overview,
      status: details.status,
      type: details.type,
      adult: details.adult,
      in_production: details.in_production,
      original_language: details.original_language,
      origin_country: details.origin_country,
      spoken_languages: details.spoken_languages?.map((lang: any) => ({
        code: lang.iso_639_1,
        name: lang.english_name || lang.name
      })) || [],
      
      // Season and episode info
      seasons: details.number_of_seasons,
      episodes: details.number_of_episodes,
      episode_run_time: details.episode_run_time,
      
      // Season details
      season_details: details.seasons?.map((season: any) => ({
        id: season.id,
        season_number: season.season_number,
        name: season.name,
        overview: season.overview,
        air_date: season.air_date,
        episode_count: season.episode_count,
        poster: season.poster_path ? `https://image.tmdb.org/t/p/w500${season.poster_path}` : null
      })) || [],
      
      genres: details.genres?.map((g: any) => ({
        id: g.id,
        name: g.name
      })) || [],
      
      networks: details.networks?.map((network: any) => ({
        id: network.id,
        name: network.name,
        logo: network.logo_path ? `https://image.tmdb.org/t/p/w500${network.logo_path}` : null,
        origin_country: network.origin_country
      })) || [],
      
      production_companies: details.production_companies?.map((company: any) => ({
        id: company.id,
        name: company.name,
        logo: company.logo_path ? `https://image.tmdb.org/t/p/w500${company.logo_path}` : null,
        origin_country: company.origin_country
      })) || [],
      
      production_countries: details.production_countries?.map((country: any) => ({
        code: country.iso_3166_1,
        name: country.name
      })) || [],
      
      // Enhanced cast and crew
      cast: details.credits?.cast?.slice(0, 15).map((c: any) => ({
        id: c.id,
        name: c.name,
        character: c.character,
        credit_id: c.credit_id,
        order: c.order,
        profile: c.profile_path ? `https://image.tmdb.org/t/p/w500${c.profile_path}` : null,
        known_for_department: c.known_for_department,
        popularity: c.popularity
      })) || [],
      
      crew: details.credits?.crew?.map((c: any) => ({
        id: c.id,
        name: c.name,
        job: c.job,
        department: c.department,
        credit_id: c.credit_id,
        profile: c.profile_path ? `https://image.tmdb.org/t/p/w500${c.profile_path}` : null,
        known_for_department: c.known_for_department,
        popularity: c.popularity
      })) || [],
      
      // Creators and key personnel
      creators: details.created_by?.map((creator: any) => ({
        id: creator.id,
        name: creator.name,
        credit_id: creator.credit_id,
        profile: creator.profile_path ? `https://image.tmdb.org/t/p/w500${creator.profile_path}` : null
      })) || [],
      
      executive_producers: details.credits?.crew?.filter((c: any) => 
        c.job === 'Executive Producer'
      ).slice(0, 5).map((c: any) => ({
        id: c.id,
        name: c.name,
        profile: c.profile_path ? `https://image.tmdb.org/t/p/w500${c.profile_path}` : null
      })) || [],
      
      // Enhanced similar shows
      similar: details.similar?.results?.slice(0, 10).map((s: any) => ({
        id: s.id,
        name: s.name,
        year: s.first_air_date ? new Date(s.first_air_date).getFullYear() : null,
        poster: s.poster_path ? `https://image.tmdb.org/t/p/w500${s.poster_path}` : null,
        rating: s.vote_average,
        overview: s.overview
      })) || [],
      
      // Recommendations
      recommendations: details.recommendations?.results?.slice(0, 10).map((r: any) => ({
        id: r.id,
        name: r.name,
        year: r.first_air_date ? new Date(r.first_air_date).getFullYear() : null,
        poster: r.poster_path ? `https://image.tmdb.org/t/p/w500${r.poster_path}` : null,
        rating: r.vote_average,
        overview: r.overview
      })) || [],
      
      // Additional metadata
      homepage: details.homepage,
      languages: details.languages,
      next_episode_to_air: details.next_episode_to_air ? {
        id: details.next_episode_to_air.id,
        name: details.next_episode_to_air.name,
        overview: details.next_episode_to_air.overview,
        air_date: details.next_episode_to_air.air_date,
        episode_number: details.next_episode_to_air.episode_number,
        season_number: details.next_episode_to_air.season_number
      } : null,
      last_episode_to_air: details.last_episode_to_air ? {
        id: details.last_episode_to_air.id,
        name: details.last_episode_to_air.name,
        overview: details.last_episode_to_air.overview,
        air_date: details.last_episode_to_air.air_date,
        episode_number: details.last_episode_to_air.episode_number,
        season_number: details.last_episode_to_air.season_number
      } : null
    };
  }

  private async enrichPersonDetails(details: any) {
    this.logger.debug('Enriching person details', { id: details.id });
    
    // Process known for items from credits
    const movieCredits = details.movie_credits?.cast?.slice(0, 10) || [];
    const tvCredits = details.tv_credits?.cast?.slice(0, 10) || [];
    const allCredits = [...movieCredits, ...tvCredits]
      .sort((a, b) => (b.popularity || 0) - (a.popularity || 0))
      .slice(0, 15);

    const knownForTitles = allCredits.map(item => item.title || item.name);

    return {
      id: details.id,
      name: details.name,
      also_known_as: details.also_known_as || [],
      biography: details.biography,
      birthday: details.birthday,
      deathday: details.deathday,
      place_of_birth: details.place_of_birth,
      known_for_department: details.known_for_department,
      gender: details.gender,
      popularity: details.popularity,
      adult: details.adult,
      imdb_id: details.imdb_id,
      homepage: details.homepage,
      profile: details.profile_path ? `https://image.tmdb.org/t/p/w500${details.profile_path}` : null,
      
      // Enhanced filmography
      known_for: knownForTitles,
      
      movie_credits: {
        cast: details.movie_credits?.cast?.map((credit: any) => ({
          id: credit.id,
          title: credit.title,
          character: credit.character,
          release_date: credit.release_date,
          year: credit.release_date ? new Date(credit.release_date).getFullYear() : null,
          poster: credit.poster_path ? `https://image.tmdb.org/t/p/w500${credit.poster_path}` : null,
          rating: credit.vote_average,
          popularity: credit.popularity,
          order: credit.order,
          credit_id: credit.credit_id
        })) || [],
        
        crew: details.movie_credits?.crew?.map((credit: any) => ({
          id: credit.id,
          title: credit.title,
          job: credit.job,
          department: credit.department,
          release_date: credit.release_date,
          year: credit.release_date ? new Date(credit.release_date).getFullYear() : null,
          poster: credit.poster_path ? `https://image.tmdb.org/t/p/w500${credit.poster_path}` : null,
          rating: credit.vote_average,
          popularity: credit.popularity,
          credit_id: credit.credit_id
        })) || []
      },
      
      tv_credits: {
        cast: details.tv_credits?.cast?.map((credit: any) => ({
          id: credit.id,
          name: credit.name,
          character: credit.character,
          first_air_date: credit.first_air_date,
          year: credit.first_air_date ? new Date(credit.first_air_date).getFullYear() : null,
          poster: credit.poster_path ? `https://image.tmdb.org/t/p/w500${credit.poster_path}` : null,
          rating: credit.vote_average,
          popularity: credit.popularity,
          episode_count: credit.episode_count,
          credit_id: credit.credit_id
        })) || [],
        
        crew: details.tv_credits?.crew?.map((credit: any) => ({
          id: credit.id,
          name: credit.name,
          job: credit.job,
          department: credit.department,
          first_air_date: credit.first_air_date,
          year: credit.first_air_date ? new Date(credit.first_air_date).getFullYear() : null,
          poster: credit.poster_path ? `https://image.tmdb.org/t/p/w500${credit.poster_path}` : null,
          rating: credit.vote_average,
          popularity: credit.popularity,
          episode_count: credit.episode_count,
          credit_id: credit.credit_id
        })) || []
      },
      
      // Combined credits sorted by popularity/date
      combined_credits: allCredits.map((credit: any) => ({
        id: credit.id,
        title: credit.title || credit.name,
        type: credit.title ? 'movie' : 'tv',
        character: credit.character,
        job: credit.job,
        department: credit.department,
        release_date: credit.release_date || credit.first_air_date,
        year: (credit.release_date || credit.first_air_date) ? 
          new Date(credit.release_date || credit.first_air_date).getFullYear() : null,
        poster: credit.poster_path ? `https://image.tmdb.org/t/p/w500${credit.poster_path}` : null,
        rating: credit.vote_average,
        popularity: credit.popularity,
        episode_count: credit.episode_count,
        order: credit.order
      })),
      
      // External IDs
      external_ids: details.external_ids ? {
        imdb_id: details.external_ids.imdb_id,
        facebook_id: details.external_ids.facebook_id,
        instagram_id: details.external_ids.instagram_id,
        twitter_id: details.external_ids.twitter_id,
        youtube_id: details.external_ids.youtube_id,
        tiktok_id: details.external_ids.tiktok_id
      } : undefined
    };
  }
}