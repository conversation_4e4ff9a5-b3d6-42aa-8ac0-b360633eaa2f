// src/services/searchService.ts
import { TMDBClient } from '../clients/tmdbClient';
import { CacheClient } from '../clients/cacheClient';
import { QueryClassifier } from './classifyQuery';
import { DynamoDBLogger } from './dynamoDbLogger';
import { Logger } from '../../common/utils/logger';
import { SearchResponse, SearchResult } from '../types';

export class SearchService {
  private tmdb: TMDBClient;
  private cache: CacheClient;
  private classifier: QueryClassifier;
  private dbLogger: DynamoDBLogger;
  private logger: Logger;

  constructor() {
    this.logger = Logger.getInstance('search-service');
    this.tmdb = new TMDBClient();
    this.cache = new CacheClient();
    this.classifier = new QueryClassifier();
    this.dbLogger = new DynamoDBLogger();
    this.logger.info('SearchService initialized');
  }

  async search(query: string, userId?: string): Promise<SearchResponse> {
    this.logger.debug('Search started', { query, userId });
    
    if (query.length < 3) {
      this.logger.warn('Query too short', { query, minLength: 3 });
      throw new Error('Query must be at least 3 characters');
    }

    const cacheKey = `search:${query}`;
    this.logger.debug('Generated cache key', { cacheKey });

    // Check cache first
    this.logger.debug('Checking cache', { cacheKey });
    const cached = await this.cache.get(cacheKey);
    if (cached) {
      this.logger.info('Cache hit', { cacheKey, type: cached.type });
      try {
        await this.dbLogger.logSearch({
          query,
          label: cached.type,
          timestamp: Date.now(),
          userId,
          source: 'cache'
        });
      } catch (error) {
        this.logger.error('Failed to log search to DynamoDB', error, { query, source: 'cache' });
      }
      return cached;
    }
    this.logger.debug('Cache miss', { cacheKey });

    // Classify query
    this.logger.debug('Classifying query', { query });
    const label = await this.classifier.classify(query);
    this.logger.info('Query classified', { query, label });

    // Search based on classification
    let tmdbResults: any;
    this.logger.debug('Initiating TMDB search', { query, label });
    switch (label) {
      case 'movie':
        tmdbResults = await this.tmdb.searchMovie(query);
        break;
      case 'tv':
        tmdbResults = await this.tmdb.searchTV(query);
        break;
      case 'person':
        tmdbResults = await this.tmdb.searchPerson(query);
        break;
      case 'genre':
        const genreId = this.classifier.getGenreId(query);
        if (genreId) {
          tmdbResults = await this.tmdb.discoverByGenre(genreId);
        } else {
          tmdbResults = await this.tmdb.searchMulti(query);
        }
        break;
      default:
        tmdbResults = await this.tmdb.searchMulti(query);
    }

    // Enrich and prune results
    const results = await this.enrichResults(tmdbResults.results || []);
    this.logger.debug('Enriched results', { initialCount: tmdbResults.results?.length || 0, enrichedCount: results.length });

    const response: SearchResponse = {
      query,
      type: label,
      results
    };

    // Cache results
    try {
      await this.cache.set(cacheKey, response, 600);
      this.logger.debug('Results cached successfully', { cacheKey, ttl: 600 });
    } catch (error) {
      this.logger.error('Failed to cache results', error, { cacheKey });
    }

    // Log search
    try {
      await this.dbLogger.logSearch({
        query,
        label,
        timestamp: Date.now(),
        userId,
        source: 'api'
      });
      this.logger.debug('Search logged to DynamoDB', { query, label });
    } catch (error) {
      this.logger.error('Failed to log search to DynamoDB', error, { query, label });
    }

    // Update trending
    try {
      await this.cache.incrementTrending(query);
      this.logger.debug('Updated trending stats', { query });
    } catch (error) {
      this.logger.error('Failed to update trending stats', error, { query });
    }

    this.logger.info('Search completed', { query, resultCount: results.length });
    return response;
  }

  private async enrichResults(results: any[]): Promise<SearchResult[]> {
    const limitedResults = results.slice(0, 10);
    const enrichedResults: SearchResult[] = [];

    // Process results in parallel with concurrency limit
    const chunks = this.chunkArray(limitedResults, 3); // Process 3 at a time
    
    for (const chunk of chunks) {
      const promises = chunk.map(item => this.enrichSingleResult(item));
      const chunkResults = await Promise.allSettled(promises);
      
      chunkResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          enrichedResults.push(result.value);
        } else {
          this.logger.error('Failed to enrich result', result.status === 'rejected' ? result.reason : 'Unknown error', { itemId: chunk[index].id });
          // Fallback to basic result
          enrichedResults.push(this.createBasicResult(chunk[index]));
        }
      });
    }

    return enrichedResults;
  }

  private async enrichSingleResult(item: any): Promise<SearchResult | null> {
    try {
      const mediaType = item.media_type || (item.title ? 'movie' : item.name && item.known_for ? 'person' : 'tv');
      
      const result: SearchResult = {
        id: item.id,
        type: mediaType
      };

      switch (mediaType) {
        case 'movie':
          return await this.enrichMovieResult(item, result);
        case 'tv':
          return await this.enrichTVResult(item, result);
        case 'person':
          return await this.enrichPersonResult(item, result);
        default:
          return this.createBasicResult(item);
      }
    } catch (error) {
      this.logger.error('Error enriching single result', error, { itemId: item.id });
      return null;
    }
  }

  private async enrichMovieResult(item: any, result: SearchResult): Promise<SearchResult> {
    // Get movie details with credits appended
    const movieDetails = await this.tmdb.getMovieDetails(item.id);

    result.title = item.title;
    result.release_year = item.release_date ? new Date(item.release_date).getFullYear() : undefined;
    result.poster = item.poster_path ? `https://image.tmdb.org/t/p/w500${item.poster_path}` : undefined;
    result.overview = movieDetails.overview;
    result.runtime = movieDetails.runtime;
    result.rating = movieDetails.vote_average;
    result.genres = movieDetails.genres?.map((g: any) => g.name);

    // Extract director and main cast from appended credits
    if (movieDetails.credits?.crew) {
      const director = movieDetails.credits.crew.find((person: any) => person.job === 'Director');
      result.director = director ? director.name : undefined;
    }

    if (movieDetails.credits?.cast) {
      result.cast = movieDetails.credits.cast.slice(0, 5).map((actor: any) => ({
        id: actor.id,
        name: actor.name,
        character: actor.character,
        profile_path: actor.profile_path ? `https://image.tmdb.org/t/p/w500${actor.profile_path}` : undefined
      }));
    }

    return result;
  }

  private async enrichTVResult(item: any, result: SearchResult): Promise<SearchResult> {
    // Get TV show details with credits appended
    const tvDetails = await this.tmdb.getTVDetails(item.id);

    result.name = item.name;
    result.release_year = item.first_air_date ? new Date(item.first_air_date).getFullYear() : undefined;
    result.poster = item.poster_path ? `https://image.tmdb.org/t/p/w500${item.poster_path}` : undefined;
    result.overview = tvDetails.overview;
    result.rating = tvDetails.vote_average;
    result.genres = tvDetails.genres?.map((g: any) => g.name);
    result.seasons = tvDetails.number_of_seasons;
    result.episodes = tvDetails.number_of_episodes;
    result.status = tvDetails.status;

    // Extract creators and main cast from appended credits
    if (tvDetails.created_by) {
      result.creators = tvDetails.created_by.map((creator: any) => creator.name);
    }

    if (tvDetails.credits?.cast) {
      result.cast = tvDetails.credits.cast.slice(0, 5).map((actor: any) => ({
        id: actor.id,
        name: actor.name,
        character: actor.character,
        profile_path: actor.profile_path ? `https://image.tmdb.org/t/p/w500${actor.profile_path}` : undefined
      }));
    }

    return result;
  }

  private async enrichPersonResult(item: any, result: SearchResult): Promise<SearchResult> {
    // Get person details with credits appended
    const personDetails = await this.tmdb.getPersonDetails(item.id);

    result.name = item.name;
    result.profile = item.profile_path ? `https://image.tmdb.org/t/p/w500${item.profile_path}` : undefined;
    result.known_for_department = personDetails.known_for_department;
    result.biography = personDetails.biography;
    result.birthday = personDetails.birthday;
    result.place_of_birth = personDetails.place_of_birth;

    // Use credits from appended response or fallback to search result
    if (personDetails.movie_credits || personDetails.tv_credits) {
      const movieCredits = personDetails.movie_credits?.cast?.slice(0, 2) || [];
      const tvCredits = personDetails.tv_credits?.cast?.slice(0, 2) || [];
      const allCredits = [...movieCredits, ...tvCredits]
        .sort((a, b) => (b.popularity || 0) - (a.popularity || 0))
        .slice(0, 3);
      
      result.known_for = allCredits.map((credit: any) => ({
        id: credit.id,
        title: credit.title || credit.name,
        type: credit.title ? 'movie' : 'tv'
      }));
    } else if (item.known_for) {
      result.known_for = item.known_for.slice(0, 3).map((k: any) => ({
        id: k.id,
        title: k.title || k.name,
        type: k.media_type
      }));
    }

    return result;
  }

  private createBasicResult(item: any): SearchResult {
    const mediaType = item.media_type || (item.title ? 'movie' : item.name && item.known_for ? 'person' : 'tv');
    
    const result: SearchResult = {
      id: item.id,
      type: mediaType
    };

    if (item.title) {
      result.title = item.title;
      result.release_year = item.release_date ? new Date(item.release_date).getFullYear() : undefined;
      result.poster = item.poster_path ? `https://image.tmdb.org/t/p/w500${item.poster_path}` : undefined;
    } else if (item.name) {
      result.name = item.name;
      if (item.known_for) {
        result.known_for = item.known_for.slice(0, 3).map((k: any) => k.title || k.name);
      }
      result.profile = item.profile_path ? `https://image.tmdb.org/t/p/w500${item.profile_path}` : undefined;
      if (item.first_air_date) {
        result.release_year = new Date(item.first_air_date).getFullYear();
      }
    }

    return result;
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}