// src/services/dynamoDbLogger.ts
import { DynamoDBClient, ResourceInUseException } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand } from '@aws-sdk/lib-dynamodb';
import { SearchLog } from '../types';
import { Logger } from '../../common/utils/logger';
import { getDynamoDBConfig, IS_LOCAL, DYNAMODB_CONFIG } from '@src/common/config/globals';

export class DynamoDBLogger {
  private docClient: DynamoDBDocumentClient;
  private tableName: string;
  private logger: Logger;
  private tableCreated: boolean = false;

  constructor() {
    this.logger = Logger.getInstance('dynamodb-logger');
    this.tableName = process.env.SEARCH_LOGS_TABLE!;
    
    if (!this.tableName) {
      this.logger.error('SEARCH_LOGS_TABLE environment variable is not set');
      throw new Error('<PERSON>ARCH_LOGS_TABLE environment variable is required');
    }
    
    const client = new DynamoDBClient({
      ...getDynamoDBConfig(),
      maxAttempts: 3
    });
    
    this.docClient = DynamoDBDocumentClient.from(client, {
      marshallOptions: {
        removeUndefinedValues: true,
      },
    });
    
    this.logger.info('DynamoDB Logger initialized', { tableName: this.tableName });
  }

  async logSearch(searchLog: SearchLog): Promise<boolean> {
    const start = Date.now();
    const logId = `${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

    // Log DynamoDB client configuration
    const dbConfig = getDynamoDBConfig();
    const endpoint = 'endpoint' in dbConfig ? dbConfig.endpoint : 'AWS Default';
    this.logger.info('DynamoDB Client Configuration:', {
      region: dbConfig.region,
      endpoint,
      tableName: this.tableName,
      isLocal: IS_LOCAL,
      nodeEnv: process.env.NODE_ENV || 'development',
      maxAttempts: 3
    });
    
    this.logger.info('Logging search to DynamoDB', {
      logId,
      tableName: this.tableName,
      searchLog: {
        query: searchLog.query,
        label: searchLog.label,
        source: searchLog.source,
        userId: searchLog.userId ? 'present' : 'missing'
      }
    });
    
    try {
      const command = new PutCommand({
        TableName: this.tableName,
        Item: {
          id: logId,
          timestamp: searchLog.timestamp || Date.now(),
          query: searchLog.query,
          label: searchLog.label,
          source: searchLog.source || 'unknown',
          ...(searchLog.userId && { userId: searchLog.userId }),
          ttl: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days TTL
        },
        ReturnConsumedCapacity: 'INDEXES',
        ReturnValues: 'NONE'
      });

    console.log('Raw config from getDynamoDBConfig():', getDynamoDBConfig());
    // Add this line in your logSearch method before the send() call
    console.log('Client config check:', JSON.stringify({
        endpoint: this.docClient.config?.endpoint,
        region: this.docClient.config?.region
    }, null, 2));
      
      const response = await this.docClient.send(command);
      
      this.logger.info('Successfully logged search to DynamoDB', {
        logId,
        consumedCapacity: response.ConsumedCapacity?.CapacityUnits,
        executionTime: `${Date.now() - start}ms`
      });
      
      return true;
    } catch (error: any) {
      if (error.name === 'ResourceNotFoundException') {
        this.logger.error('DynamoDB table does not exist', error, {
          tableName: this.tableName,
          logId
        });
      } else if (error.name === 'AccessDeniedException') {
        this.logger.error('Permission denied when accessing DynamoDB', error, {
          tableName: this.tableName,
          logId,
          action: 'dynamodb:PutItem'
        });
      } else if (error.name === 'ProvisionedThroughputExceededException') {
        this.logger.warn('DynamoDB throughput exceeded', {
          tableName: this.tableName,
          logId,
          retryable: true
        });
      } else {
        this.logger.error('Failed to log search to DynamoDB', error, {
          tableName: this.tableName,
          logId,
          errorName: error.name,
          errorCode: error.code,
          statusCode: error.statusCode
        });
      }
      
      return false;
    }
  }
}
