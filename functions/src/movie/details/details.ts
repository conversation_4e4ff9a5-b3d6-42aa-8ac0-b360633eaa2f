// src/handlers/details.ts
import { APIGatewayProxyHandler } from 'aws-lambda';
import { DetailsService } from '../services/detailsService';

const detailsService = new DetailsService();

export const movieDetailsHandler: APIGatewayProxyHandler = async (event) => {
  try {
    const id = parseInt(event.pathParameters?.id || '0');
    if (!id) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Invalid movie ID' })
      };
    }

    const details = await detailsService.getMovieDetails(id);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify(details)
    };
  } catch (error) {
    console.error('Movie details error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};

export const tvDetailsHandler: APIGatewayProxyHandler = async (event) => {
  try {
    const id = parseInt(event.pathParameters?.id || '0');
    if (!id) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Invalid TV show ID' })
      };
    }

    const details = await detailsService.getTVDetails(id);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify(details)
    };
  } catch (error) {
    console.error('TV details error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};

export const personDetailsHandler: APIGatewayProxyHandler = async (event) => {
  try {
    const id = parseInt(event.pathParameters?.id || '0');
    if (!id) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Invalid person ID' })
      };
    }

    const details = await detailsService.getPersonDetails(id);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify(details)
    };
  } catch (error) {
    console.error('Person details error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
