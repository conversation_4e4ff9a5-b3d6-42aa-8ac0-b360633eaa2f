// src/handlers/search.ts
import { APIGatewayProxyHandler } from 'aws-lambda';
import { SearchService } from '../services/searchService';
import { Logger } from '../../common/utils/logger';

const searchService = new SearchService();
const logger = Logger.getInstance('search-handler');

export const handler: APIGatewayProxyHandler = async (event, context) => {
  const startTime = Date.now();
  const requestId = event.requestContext.requestId;
  const query = event.queryStringParameters?.q;
  const userId = event.requestContext.authorizer?.userId;
  const requestContext = {
    requestId,
    query,
    userId,
    path: event.path,
    httpMethod: event.httpMethod,
    sourceIp: event.requestContext.identity?.sourceIp,
    userAgent: event.requestContext.identity?.userAgent
  };

  logger.info('Search request received', {
    ...requestContext,
    headers: {
      'user-agent': event.headers?.['user-agent'],
      'x-forwarded-for': event.headers?.['x-forwarded-for']
    }
  });

  const baseHeaders = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'x-request-id': requestId
  } as const;

  try {
    if (!query) {
      logger.warn('Missing required query parameter', { 
        ...requestContext,
        validationError: 'Query parameter q is required'
      });
      
      return {
        statusCode: 400,
        headers: baseHeaders,
        body: JSON.stringify({ 
          error: 'Bad Request',
          message: 'Query parameter q is required',
          requestId
        })
      };
    }

    logger.debug('Initiating search', { 
      ...requestContext,
      queryLength: query.length 
    });

    const result = await searchService.search(query, userId);
    const executionTime = Date.now() - startTime;

    logger.info('Search completed successfully', {
      ...requestContext,
      resultCount: Array.isArray(result) ? result.length : 1,
      executionTime: `${executionTime}ms`,
      memoryUsage: process.memoryUsage().rss / (1024 * 1024) // in MB
    });

    return {
      statusCode: 200,
      headers: {
        ...baseHeaders,
        'x-execution-time': `${executionTime}ms`,
        'x-cache': 'MISS'
      },
      body: JSON.stringify({
        ...result,
        meta: {
          requestId,
          timestamp: new Date().toISOString()
        }
      })
    };
  } catch (error) {
    const executionTime = Date.now() - startTime;
    const errorContext = {
      ...requestContext,
      executionTime: `${executionTime}ms`,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack?.split('\n').slice(0, 5).join('\n') // First 5 lines of stack
      } : 'Unknown error'
    };

    logger.error('Search operation failed', error, errorContext);

    if (error instanceof Error && error.message.includes('3 characters')) {
      return {
        statusCode: 400,
        headers: baseHeaders,
        body: JSON.stringify({
          error: 'Bad Request',
          message: error.message,
          requestId
        })
      };
    }

    // Handle specific error types if needed
    const isClientError = error instanceof Error && 
      ['ValidationError', 'BadRequestError'].includes(error.name);

    if (isClientError) {
      return {
        statusCode: 400,
        headers: baseHeaders,
        body: JSON.stringify({
          error: 'Bad Request',
          message: error.message,
          requestId
        })
      };
    }

    // Default error response
    return {
      statusCode: 500,
      headers: baseHeaders,
      body: JSON.stringify({
        error: 'Internal Server Error',
        message: 'An unexpected error occurred',
        requestId,
        timestamp: new Date().toISOString()
      })
    };
  }
};