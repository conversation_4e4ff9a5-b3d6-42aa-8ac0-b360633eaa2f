// src/handlers/trending.ts
import { APIGatewayProxyHandler } from 'aws-lambda';
import { CacheClient } from '../clients/cacheClient';

const cache = new CacheClient();

export const handler: APIGatewayProxyHandler = async (event) => {
  try {
    const limit = parseInt(event.queryStringParameters?.limit || '10');
    const trending = await cache.getTrending(limit);

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({ trending })
    };
  } catch (error) {
    console.error('Trending error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};