import { APIGatewayAuthorizerResult, Context, APIGatewayTokenAuthorizerEvent } from 'aws-lambda';
import axios from 'axios';
import jwt, { JwtHeader } from 'jsonwebtoken';

// Replace with your actual Firebase Project ID
const PROJECT_ID = 'thyview-dev-9ce2f';
const ISSUER = `https://securetoken.google.com/${PROJECT_ID}`;
const KEY_CACHE_TTL_MS = 24 * 60 * 60 * 1000; // 24 hours

let cachedKeys: Record<string, string> | null = null;
let lastKeyFetchTime = 0;

async function getGooglePublicKeys(): Promise<Record<string, string>> {
  const now = Date.now();
  if (!cachedKeys || now - lastKeyFetchTime > KEY_CACHE_TTL_MS) {
    const response = await axios.get<Record<string, string>>(
      'https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>'
    );
    cachedKeys = response.data;
    lastKeyFetchTime = now;
  }
  return cachedKeys;
}

export const handler = async (
  event: APIGatewayTokenAuthorizerEvent,
  _context: Context
): Promise<APIGatewayAuthorizerResult> => {
  console.log('Authorizer invoked');

  const token = event.authorizationToken?.split(' ')[1]?.trim();
  if (!token) {
    console.error('No token provided');
    throw new Error('Unauthorized');
  }

  let decodedToken: jwt.JwtPayload;
  let uid: string;

  try {
    const keys = await getGooglePublicKeys();
    const decodedHeader = jwt.decode(token, { complete: true }) as { header: JwtHeader };

    if (!decodedHeader?.header?.kid || !keys[decodedHeader.header.kid]) {
      console.error('Invalid key ID');
      throw new Error('Unauthorized');
    }

    const cert = keys[decodedHeader.header.kid];
    decodedToken = jwt.verify(token, cert, {
      algorithms: ['RS256'],
      audience: PROJECT_ID,
      issuer: ISSUER,
    }) as jwt.JwtPayload;

    uid = decodedToken.uid || decodedToken.user_id || decodedToken.sub;

    if (!uid) {
      console.error('UID missing in token');
      throw new Error('Unauthorized');
    }

  } catch (err) {
    console.error('Token verification failed:', err);
    throw new Error('Unauthorized');
  }

  console.log('Authorization successful for UID:', uid);

  const parts = event.methodArn.split('/');
  const resourceArn = parts.slice(0, 2).join('/') + '/*';

  return generatePolicy(uid, 'Allow', resourceArn, { uid });
};

function generatePolicy(
  principalId: string,
  effect: 'Allow' | 'Deny',
  resource: string,
  context: Record<string, any>
): APIGatewayAuthorizerResult {
  return {
    principalId,
    policyDocument: {
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'execute-api:Invoke',
          Effect: effect,
          Resource: resource,
        },
      ],
    },
    context,
  };
}
