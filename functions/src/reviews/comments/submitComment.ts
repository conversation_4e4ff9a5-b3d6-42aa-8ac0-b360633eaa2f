// functions/submitComment.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';
import { ApiError, getAuth, lambdaHandler } from '../../common/utils/aws-helpers';
import { CommentModel, CommentDocument } from '../../common/models/comments-models';
import { PostModel, PostDocument } from '../../common/models/posts-models';
import { UserModel } from '../../common/models/users-models';

interface SubmitCommentData {
  postId: string;
  text: string;
  parentCommentId?: string;
  imageUrl?: string;
}

interface SubmitCommentResponse {
  commentId: string;
}

const submitCommentHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<SubmitCommentResponse> => {
  // Parse request body
  const data: SubmitCommentData = JSON.parse(event.body || '{}');
  const auth = getAuth(event);
  
  if (!auth) throw new ApiError('unauthenticated', 'User must be signed in');
  
  const { postId, text, parentCommentId, imageUrl } = data;
  if (!postId || !text) {
    throw new ApiError('invalid-argument', 'postId and text are required');
  }

  // Get user profile to fetch externalAuthorId using dynamoose
  const user = await UserModel.get({ id: auth.uid });
  if (!user || !user.externalAuthorId) {
    throw new ApiError('not-found', 'User profile not found');
  }
  
  // Generate a new comment ID
  const commentId = uuidv4();
  const now = Math.floor(Date.now() / 1000); // Use numeric timestamp
  
  // Create the comment using dynamoose
  const newComment: Partial<CommentDocument> = {
    id: commentId,
    postId: postId,
    externalAuthorId: user.externalAuthorId,
    text: text,
    parentCommentId: parentCommentId || 'null',
    imageUrl: imageUrl || null,
    createdAt: now,
    likeCount: 0,
    replyCount: 0,
    username: user.displayName || user.username || '(unknown)'
  };
  
  await CommentModel.create(newComment);

  // Increment top-level commentCount only if this is not a reply
  if (!parentCommentId) {
    const post = await PostModel.get({ id: postId });
    if (post) {
      const updateData: Partial<PostDocument> = {
        commentCount: post.commentCount + 1,
        updatedAt: now
      };
      await PostModel.update({ id: postId }, updateData);
    }
  } else {
    // If this is a reply, increment the parent comment's replyCount
    const parentComment = await CommentModel.get({ id: parentCommentId });
    if (parentComment) {
      const updateData: Partial<CommentDocument> = {
        replyCount: parentComment.replyCount + 1
      };
      await CommentModel.update({ id: parentCommentId }, updateData);
    }
  }
  
  return { commentId };
};

// Export the Lambda handler
export const submitComment = lambdaHandler(submitCommentHandler);
