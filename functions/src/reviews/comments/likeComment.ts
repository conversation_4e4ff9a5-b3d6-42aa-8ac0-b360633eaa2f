// functions/likeComment.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ApiError, getAuth, lambdaHandler } from '../../common/utils/aws-helpers';
import { CommentLikeModel, CommentDocument } from '../../common/models/comments-models';
import { CommentModel } from '../../common/models/comments-models';

interface LikeCommentData {
  postId: string;
  commentId: string;
}

interface LikeCommentResponse {
  liked: boolean;
}

const likeCommentHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<LikeCommentResponse> => {
  // Parse request body
  const data: LikeCommentData = JSON.parse(event.body || '{}');
  const auth = getAuth(event);
  
  if (!auth) throw new ApiError('unauthenticated', 'User must be signed in to like a comment.');
  
  const { postId, commentId } = data;
  if (!postId || !commentId) {
    throw new ApiError('invalid-argument', 'Both postId and commentId are required.');
  }

  // Check if user already liked this comment using dynamoose
  const existingLike = await CommentLikeModel.get({
    commentId: commentId,
    userId: auth.uid
  });
  
  const now = Math.floor(Date.now() / 1000); // Use numeric timestamp
  let liked: boolean;
  
  if (existingLike) {
    // User already liked - remove like using dynamoose
    await CommentLikeModel.delete({
      commentId: commentId,
      userId: auth.uid
    });
    
    // Decrement like count in comment using dynamoose
    const comment = await CommentModel.get({ id: commentId });
    if (comment) {
      const updateData: Partial<CommentDocument> = {
        likeCount: Math.max(0, (comment.likeCount || 1) - 1)
      };
      await CommentModel.update({ id: commentId }, updateData);
    }
    
    liked = false;
  } else {
    // User hasn't liked - add like using dynamoose
    await CommentLikeModel.create({
      commentId: commentId,
      userId: auth.uid,
      postId: postId,  // For queries across posts
      likedAt: now
    });
    
    // Increment like count in comment using dynamoose
    const comment = await CommentModel.get({ id: commentId });
    if (comment) {
      const updateData: Partial<CommentDocument> = {
        likeCount: (comment.likeCount || 0) + 1
      };
      await CommentModel.update({ id: commentId }, updateData);
    }
    
    liked = true;
  }

  return { liked };
};

// Export the Lambda handler
export const likeComment = lambdaHandler(likeCommentHandler);
