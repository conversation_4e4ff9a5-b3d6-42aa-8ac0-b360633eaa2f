// src/functions/getReplies.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ApiError, getAuth, lambdaHandler } from '../../common/utils/aws-helpers';
import { CommentModel, CommentLikeModel } from '../../common/models/comments-models';

interface GetRepliesData {
  postId: string;
  commentId: string;
}

interface Reply {
    id: string;
    externalAuthorId: string;
    username: string;           // denormalized display name
    parentCommentId: string;    // the comment this is replying to
    text: string;
    imageUrl: string | null;
    createdAt: string;          // ISO8601 timestamp
    likeCount: number;          // total likes on this reply
    liked: boolean;             // whether current user has liked it
}

// Define type for database item

interface GetRepliesResponse {
  replies: Reply[];
}

const getRepliesHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<GetRepliesResponse> => {
  // Parse request body
  const data: GetRepliesData = JSON.parse(event.body || '{}');
  const auth = getAuth(event);
  
  const { postId, commentId } = data;
  if (!postId || !commentId) {
    throw new ApiError('invalid-argument', 'postId and commentId are required');
  }

  // Query for replies (where parentCommentId matches the given commentId) using dynamoose
  const repliesQuery = CommentModel.scan()
    .filter('postId').eq(postId)
    .filter('parentCommentId').eq(commentId);
  
  const repliesResponse = await repliesQuery.exec();
  
  // Sort the response manually - ensure createdAt is treated as a number
  repliesResponse.sort((a, b) => {
    const timeA = typeof a.createdAt === 'number' ? a.createdAt : parseInt(a.createdAt);
    const timeB = typeof b.createdAt === 'number' ? b.createdAt : parseInt(b.createdAt);
    return timeA - timeB;
  });
  
  // Map to Replies DTO
  const replies: Reply[] = repliesResponse.map(reply => ({
    id: reply.id,
    externalAuthorId: reply.externalAuthorId,
    username: reply.username || '(unknown)',  // Use username from document
    parentCommentId: reply.parentCommentId,
    text: reply.text,
    imageUrl: reply.imageUrl || null,
    createdAt: reply.createdAt.toString(),
    likeCount: reply.likeCount || 0,
    liked: false  // Will be updated if auth is present
  }));

  // Check if user has liked any of these replies
  if (auth && replies.length > 0) {
    const likeCheckPromises = replies.map(reply => 
      CommentLikeModel.get({
        commentId: reply.id,
        userId: auth.uid
      })
    );
    
    const likeResponses = await Promise.all(likeCheckPromises);
    
    replies.forEach((reply, index) => {
      reply.liked = !!likeResponses[index];
    });
  }
  
  return { replies };
};

// Export the Lambda handler
export const getReplies = lambdaHandler(getRepliesHandler);
