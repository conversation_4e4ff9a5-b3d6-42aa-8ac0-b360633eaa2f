// src/functions/deleteComment.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ApiError, getAuth, getPathParameters, lambdaHandler } from '../../common/utils/aws-helpers';
import { CommentModel } from '../../common/models/comments-models';
import { PostModel, PostDocument } from '../../common/models/posts-models';
import { UserModel } from '../../common/models/users-models';

interface DeleteCommentResponse {
  success: boolean;
}

const deleteCommentHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<DeleteCommentResponse> => {
  // Extract comment ID from path parameters
  const pathParams = getPathParameters(event);
  const auth = getAuth(event);
  
  if (!auth) throw new ApiError('unauthenticated', 'User must be signed in');
  
  const commentId = pathParams.commentId;
  if (!commentId) {
    throw new ApiError('invalid-argument', 'commentId is required');
  }
  
  // Get the comment to verify ownership using dynamoose
  const comment = await CommentModel.get({ id: commentId });
  
  if (!comment) {
    throw new ApiError('not-found', 'Comment not found');
  }
  
  const postId = comment.postId;
  
  // Verify ownership by checking if the comment's externalAuthorId matches
  // We need to get the user's externalAuthorId and compare
  const user = await UserModel.get({ id: auth.uid });
  if (!user || !user.externalAuthorId) {
    throw new ApiError('not-found', 'User profile not found');
  }
  
  // If the user isn't the author of the comment, deny access
  if (comment.externalAuthorId !== user.externalAuthorId) {
    throw new ApiError('permission-denied', 'Can only delete your own comments');
  }
  
  // Find replies to this comment using dynamoose
  const repliesQuery = CommentModel.scan()
    .filter('postId').eq(postId)
    .filter('parentCommentId').eq(commentId);
  
  const replies = await repliesQuery.exec();
  
  // Delete the comment using dynamoose
  await CommentModel.delete({ id: commentId });
  
  // Delete all replies using dynamoose
  const deletePromises = replies.map(reply => 
    CommentModel.delete({ id: reply.id })
  );
  
  await Promise.all(deletePromises);
  
  // Update post comment count using dynamoose
  const totalDeleted = 1 + replies.length;
  
  const post = await PostModel.get({ id: postId });
  if (post) {
    const now = Math.floor(Date.now() / 1000); // Numeric timestamp
    const updateData: Partial<PostDocument> = {
      commentCount: Math.max(0, (post.commentCount || 0) - totalDeleted),
      updatedAt: now
    };
    await PostModel.update({ id: postId }, updateData);
  }
  
  return { success: true };
};

// Export the Lambda handler
export const deleteComment = lambdaHandler(deleteCommentHandler);
