// functions/getComments.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { Api<PERSON>rror, getAuth, lambdaHandler } from '../../common/utils/aws-helpers';
import { CommentLikeModel } from '../../common/models/comments-models';
import { CommentModel } from '../../common/models/comments-models';

interface GetCommentsData { 
  postId: string 
}

interface CommentDTO {
  id: string;
  externalAuthorId: string;
  text: string;
  parentCommentId: string | null;
  imageUrl: string | null;
  createdAt: string;
  replyCount: number;
  likeCount: number;
  liked: boolean;
}

interface GetCommentsResponse { 
  comments: CommentDTO[] 
}

const getCommentsHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<GetCommentsResponse> => {
  // Parse request body
  const data: GetCommentsData = JSON.parse(event.body || '{}');
  const auth = getAuth(event);
  
  const { postId } = data;
  if (!postId) throw new ApiError('invalid-argument', 'postId required');

  // Query for top-level comments (where parentCommentId is null) using dynamoose
  const commentsQuery = CommentModel.scan()
    .filter('postId').eq(postId)
    .filter('parentCommentId').eq('null');
  
  const commentsResponse = await commentsQuery.exec();
  
  // Sort the response manually since scan() doesn't have a sort method
  commentsResponse.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
  
  // Map to DTO
  const comments: CommentDTO[] = commentsResponse.map(comment => ({
    id: comment.id,
    externalAuthorId: comment.externalAuthorId,
    text: comment.text,
    parentCommentId: comment.parentCommentId === 'null' ? null : comment.parentCommentId,
    imageUrl: comment.imageUrl || null,
    createdAt: comment.createdAt.toString(),
    replyCount: comment.replyCount || 0,
    likeCount: comment.likeCount || 0,
    liked: false  // Will be updated if auth is present
  }));

  // Check if user has liked any of these comments
  if (auth && comments.length > 0) {
    const likeCheckPromises = comments.map(comment => 
      CommentLikeModel.get({
        commentId: comment.id,
        userId: auth.uid
      })
    );
    
    const likeResponses = await Promise.all(likeCheckPromises);
    
    comments.forEach((comment: CommentDTO, index: number) => {
      comment.liked = !!likeResponses[index];
    });
  }
  
  return { comments };
};

// Export the Lambda handler
export const getComments = lambdaHandler(getCommentsHandler);
