// src/functions/reportPost.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ApiError, getAuth, getPathParameters, lambdaHandler } from '../../common/utils/aws-helpers';
import { PostReportModel } from '../../common/models/reports-models';

interface ReportPostResponse {
  success: boolean;
}

const reportPostHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<ReportPostResponse> => {
  // Extract post ID from path parameters
  const pathParams = getPathParameters(event);
  const auth = getAuth(event);

  if (!auth) throw new ApiError('unauthenticated', 'Need to sign in');

  const postId = pathParams.postId;

  // Parse the body to get the reason
  let reason = '';
  if (event.body) {
    try {
      const body = JSON.parse(event.body);
      reason = body.reason || 'No reason provided';
    } catch (e) {
      reason = 'No reason provided';
    }
  }

  if (!postId) {
    throw new ApiError('invalid-argument', 'postId is required');
  }

  // Create a report record with a composite ID
  const reportId = `${postId}_${auth.uid}`;
  const now = Math.floor(Date.now() / 1000); // Use numeric timestamp

  // Create the report using dynamoose
  await PostReportModel.create({
    id: reportId,
    postId: postId,
    userId: auth.uid,
    reason: reason,
    reportedAt: now
  });
  
  return { success: true };
};

// Export the Lambda handler
export const reportPost = lambdaHandler(reportPostHandler);
