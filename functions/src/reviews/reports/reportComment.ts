// src/functions/reportComment.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ApiError, getAuth, lambdaHandler } from '../../common/utils/aws-helpers';
import { CommentReportModel } from '../../common/models/reports-models';

interface ReportCommentData {
  postId: string;
  commentId: string;
  reason: string;
}

interface ReportCommentResponse {
  success: boolean;
}

const reportCommentHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<ReportCommentResponse> => {
  // Parse request body
  const data: ReportCommentData = JSON.parse(event.body || '{}');
  const auth = getAuth(event);
  
  if (!auth) throw new ApiError('unauthenticated', 'Need to sign in');
  
  const { postId, commentId, reason } = data;
  if (!postId || !commentId || !reason) {
    throw new ApiError('invalid-argument', 'postId + commentId + reason required');
  }

  const docId = `${commentId}_on_${postId}`;
  const now = Math.floor(Date.now() / 1000); // Use numeric timestamp

  // Create a report record using dynamoose
  await CommentReportModel.create({
    id: `${docId}_${auth.uid}`,  // Composite key
    commentId: commentId,
    postId: postId,
    userId: auth.uid,
    reason: reason,
    reportedAt: now
  });
  
  return { success: true };
};

// Export the Lambda handler
export const reportComment = lambdaHandler(reportCommentHandler);
