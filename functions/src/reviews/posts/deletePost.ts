// src/functions/deletePost.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ApiError, getAuth, lambdaHandler } from '../../common/utils/aws-helpers';
import { PostModel } from '../../common/models/posts-models';

interface DeletePostData {
  postId: string;
}

interface DeletePostResponse {
  success: boolean;
}

const deletePostHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<DeletePostResponse> => {
  // Parse request body
  const data: DeletePostData = JSON.parse(event.body || '{}');
  const auth = getAuth(event);
  
  if (!auth) throw new ApiError('unauthenticated', 'User must be signed in');
  
  const { postId } = data;
  if (!postId) throw new ApiError('invalid-argument', 'postId is required');

  // Get the post to check ownership using dynamoose
  const post = await PostModel.get({ id: postId });
  
  if (!post) {
    throw new ApiError('not-found', 'Post not found');
  }
  
  if (post.authorUid !== auth.uid) {
    throw new ApiError('permission-denied', 'Not your post');
  }

  // Delete the post using dynamoose
  await PostModel.delete({ id: postId });
  
  return { success: true };
};

// Export the Lambda handler
export const deletePost = lambdaHandler(deletePostHandler);
