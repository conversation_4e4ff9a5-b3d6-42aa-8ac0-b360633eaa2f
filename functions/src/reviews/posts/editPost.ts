// src/functions/editPost.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ApiError, getAuth, serverTimestamp, getPathParameters, lambdaHandler } from '../../common/utils/aws-helpers';
import { PostModel } from '../../common/models/posts-models';

interface EditPostResponse {
  success: boolean;
}

const editPostHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<EditPostResponse> => {
  // Extract post ID from path parameters
  const pathParams = getPathParameters(event);
  const auth = getAuth(event);
  
  if (!auth) throw new ApiError('unauthenticated', 'User must be signed in');
  
  const postId = pathParams.postId;
  if (!postId) throw new ApiError('invalid-argument', 'postId is required');
  
  // Parse the request body
  let text, imageUrl;
  if (event.body) {
    try {
      const body = JSON.parse(event.body);
      text = body.text;
      imageUrl = body.imageUrl;
    } catch (e) {
      throw new ApiError('invalid-argument', 'Invalid request body');
    }
  }

  if (text === undefined && imageUrl === undefined) {
    throw new ApiError('invalid-argument', 'text or imageUrl required');
  }

  // Get the post using dynamoose to check ownership
  const post = await PostModel.get({ id: postId });
  
  if (!post) {
    throw new ApiError('not-found', 'Post not found');
  }
  
  if (post.authorUid !== auth.uid) {
    throw new ApiError('permission-denied', 'Not your post');
  }

  // Build the update object
  const updateData: { [key: string]: any } = {
    updatedAt: serverTimestamp()
  };
  
  // Add text if provided
  if (text !== undefined) {
    updateData.text = text;
  }
  
  // Add imageUrl if provided
  if (imageUrl !== undefined) {
    updateData.imageUrl = imageUrl;
  }
  
  // Update the post using dynamoose
  await PostModel.update({ id: postId }, updateData);
  
  return { success: true };
};

// Export the Lambda handler
export const editPost = lambdaHandler(editPostHandler);
