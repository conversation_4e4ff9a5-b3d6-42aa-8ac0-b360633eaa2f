// src/functions/likePost.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ApiError, getAuth, serverTimestamp, lambdaHandler } from '../../common/utils/aws-helpers';
import { PostModel, PostLikeModel, PostDocument } from '../../common/models/posts-models';

interface LikePostData {
  postId: string;
}

interface LikePostResponse {
  liked: boolean;
}

const likePostHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<LikePostResponse> => {
  // Parse request body
  const data: LikePostData = JSON.parse(event.body || '{}');
  const auth = getAuth(event);
  
  if (!auth) throw new ApiError('unauthenticated', 'User must be signed in');
  
  const { postId } = data;
  if (!postId) throw new ApiError('invalid-argument', 'postId is required');

  // Check if user already liked this post using dynamoose
  const existingLike = await PostLikeModel.get({
    postId: postId,
    userId: auth.uid
  });
  
  const now = Math.floor(Date.now() / 1000); // Use numeric timestamp
  let liked: boolean;
  
  if (existingLike) {
    // User already liked - remove like using dynamoose
    await PostLikeModel.delete({
      postId: postId,
      userId: auth.uid
    });
    
    // Decrement like count in post using dynamoose
    const post = await PostModel.get({ id: postId });
    if (post) {
      const updateData: Partial<PostDocument> = {
        likeCount: Math.max(0, (post.likeCount || 1) - 1),
        updatedAt: now
      };
      await PostModel.update({ id: postId }, updateData);
    }
    
    liked = false;
  } else {
    // User hasn't liked - add like using dynamoose
    await PostLikeModel.create({
      postId: postId,
      userId: auth.uid,
      likedAt: now
    });
    
    // Increment like count in post using dynamoose
    const post = await PostModel.get({ id: postId });
    if (post) {
      const updateData: Partial<PostDocument> = {
        likeCount: (post.likeCount || 0) + 1,
        updatedAt: now
      };
      await PostModel.update({ id: postId }, updateData);
    }
    
    liked = true;
  }

  return { liked };
};

// Export the Lambda handler
export const likePost = lambdaHandler(likePostHandler);
