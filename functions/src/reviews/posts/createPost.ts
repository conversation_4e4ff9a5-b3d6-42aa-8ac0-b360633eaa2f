// src/functions/createPost.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';
import { ApiError, getAuth, lambdaHandler } from '../../common/utils/aws-helpers';
import { PostModel, PostDocument } from '../../common/models/posts-models';
import { UserModel } from '../../common/models/users-models';

// Define Types
interface CreatePostData {
  text?: string;
  imageUrl?: string;
}

interface CreatePostResponse {
  postId: string;
}

const createPostHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<CreatePostResponse> => {
  // Parse request body
  const data: CreatePostData = JSON.parse(event.body || '{}');
  const auth = getAuth(event);

  if (!auth) throw new ApiError('unauthenticated', 'User must be authenticated.');

  const { text, imageUrl } = data;

  // Validation
  if (!text && !imageUrl) {
    throw new ApiError('invalid-argument', 'Post must have text or image.');
  }

  // Fetch user profile data using Dynamoose
  const user = await UserModel.get({ id: auth.uid });

  if (!user || !user.externalAuthorId) {
    throw new ApiError('not-found', 'User external ID not found.');
  }

  const postId = uuidv4();
  const now = Math.floor(Date.now() / 1000); // Use numeric timestamp

  // Create post using Dynamoose with Partial<PostDocument>
  const newPost: Partial<PostDocument> = {
    id: postId,
    authorUid: auth.uid,
    externalAuthorId: user.externalAuthorId,
    profileImageUrl: user.profileImageUrl || null,
    username: user.username || '(unknown)',
    text: text || "",
    imageUrl: imageUrl || null,
    likeCount: 0,
    commentCount: 0,
    createdAt: now,
    updatedAt: now
  };
  
  await PostModel.create(newPost);
  
  return { postId };
};

// Export the Lambda handler with consistent naming pattern
export const createPost = lambdaHandler(createPostHandler);
