// functions/getPostDetail.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ApiError, getAuth, getPathParameters, lambdaHandler } from '../../common/utils/aws-helpers';
import { PostModel, PostLikeModel } from '../../common/models/posts-models';
import { CommentModel } from '../../common/models/comments-models';

interface PostDetail {
  id: string;
  externalAuthorId: string;
  username: string;            // new
  profileImageUrl: string | null;
  text: string;
  imageUrl: string | null;
  likeCount: number;
  commentCount: number;
  createdAt: string;
  liked: boolean;
}

interface Comment {
  id: string;
  externalAuthorId: string;
  text: string;
  parentCommentId: string | null;
  imageUrl: string | null;
  createdAt: string;
  replyCount: number;
}


interface GetPostDetailResponse {
  post: PostDetail;
  comments: Comment[];
}

const getPostDetailHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<GetPostDetailResponse> => {
  // Get path parameters
  const pathParams = getPathParameters(event);
  const auth = getAuth(event);
  
  const postId = pathParams.postId;
  if (!postId) throw new ApiError('invalid-argument', 'postId is required');

  // Get post details using dynamoose
  const post = await PostModel.get({ id: postId });
  
  if (!post) {
    throw new ApiError('not-found', 'Post not found');
  }
  
  const postDetail: PostDetail = {
    id: post.id,
    externalAuthorId: post.externalAuthorId,
    username: post.username || '(unknown)',
    profileImageUrl: post.profileImageUrl || null,
    text: post.text,
    imageUrl: post.imageUrl || null,
    likeCount: post.likeCount || 0,
    commentCount: post.commentCount || 0,
    createdAt: post.createdAt.toString(),
    liked: false
  };

  // Check if user has liked this post using dynamoose
  if (auth) {
    const like = await PostLikeModel.get({
      postId,
      userId: auth.uid
    });
    
    postDetail.liked = !!like;
  }

  // Get top-level comments using dynamoose
  const commentsQuery = CommentModel.scan()
    .filter('postId').eq(postId)
    .filter('parentCommentId').eq('null');
  
  const commentsResponse = await commentsQuery.exec();
  
  // Sort the comments manually since scan() doesn't have a sort method
  commentsResponse.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
  
  const comments: Comment[] = commentsResponse.map(comment => ({
    id: comment.id,
    externalAuthorId: comment.externalAuthorId,
    text: comment.text,
    parentCommentId: null,
    imageUrl: comment.imageUrl || null,
    createdAt: comment.createdAt.toString(),
    replyCount: comment.replyCount || 0
  }));

  return { post: postDetail, comments };
};

// Export the Lambda handler
export const getPostDetail = lambdaHandler(getPostDetailHandler);
