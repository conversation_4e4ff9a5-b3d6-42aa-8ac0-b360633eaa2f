// functions/getPostsList.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ApiError, getAuth, getQueryParameters, lambdaHandler } from '../../common/utils/aws-helpers';
import { PostModel, PostLikeModel } from '../../common/models/posts-models';

interface Post {
  id: string;
  externalAuthorId: string;
  username: string;
  profileImageUrl: string | null;
  text: string;
  imageUrl: string | null;
  likeCount: number;
  commentCount: number;
  createdAt: string;
  liked: boolean;
}

interface GetPostsResponse {
  posts: Post[];
  nextToken?: string;
}

const getPostsListHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<GetPostsResponse> => {
  // Get query parameters
  const queryParams = getQueryParameters(event);
  const auth = getAuth(event);
  
  const limit = queryParams.limit ? parseInt(queryParams.limit) : 10;
  const startAfter = queryParams.startAfter || undefined;

  if (limit < 1 || limit > 50) {
    throw new ApiError('invalid-argument', 'Limit must be 1–50');
  }

  let postsQuery = PostModel.scan();
  
  // Set the limit for pagination
  postsQuery = postsQuery.limit(limit);
  
  // If we're paginating
  if (startAfter) {
    try {
      const lastEvaluatedKey = JSON.parse(Buffer.from(startAfter, 'base64').toString('utf-8'));
      postsQuery = postsQuery.startAt(lastEvaluatedKey);
    } catch (err) {
      throw new ApiError('invalid-argument', 'Invalid pagination token');
    }
  }
  
  // If filtering by author
  const authorId = queryParams.authorId;
  if (authorId) {
    postsQuery = postsQuery.filter('externalAuthorId').eq(authorId);
  }
  
  // Execute the query
  const postsResponse = await postsQuery.exec();
  
  // Sort posts by createdAt in descending order (newer first) manually
  postsResponse.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  
  // Map the response to the expected format
  const posts: Post[] = postsResponse.map(post => ({
    id: post.id,
    externalAuthorId: post.externalAuthorId,
    username: post.username || '(unknown)',
    profileImageUrl: post.profileImageUrl || null,
    text: post.text,
    imageUrl: post.imageUrl || null,
    likeCount: post.likeCount || 0,
    commentCount: post.commentCount || 0,
    createdAt: post.createdAt.toString(),
    liked: false // Will be updated if auth is present
  }));

  // If authenticated, check which posts the user has liked
  if (auth && posts.length > 0) {
    const likeCheckPromises = posts.map(post => 
      PostLikeModel.get({
        postId: post.id,
        userId: auth.uid
      })
    );
    
    const likeResponses = await Promise.all(likeCheckPromises);
    
    posts.forEach((post, index) => {
      post.liked = !!likeResponses[index];
    });
  }

  // Generate next pagination token if needed
  const response: GetPostsResponse = { posts };
  
  if (postsResponse.lastKey) {
    response.nextToken = Buffer.from(
      JSON.stringify(postsResponse.lastKey)
    ).toString('base64');
  }

  return response;
};

// Export the Lambda handler
export const getPostsList = lambdaHandler(getPostsListHandler);
