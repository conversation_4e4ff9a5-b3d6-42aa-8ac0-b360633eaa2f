// src/functions/hasUserLiked.ts
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ApiError, getAuth, lambdaHandler } from '../../common/utils/aws-helpers';
import { PostLikeModel } from '../../common/models/posts-models';

interface HasUserLikedData {
  postId: string;
}

interface HasUserLikedResponse {
  liked: boolean;
}

const hasUserLikedHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<HasUserLikedResponse> => {
  // Parse request body
  const data: HasUserLikedData = JSON.parse(event.body || '{}');
  const auth = getAuth(event);
  
  if (!auth) throw new ApiError('unauthenticated', 'User must be signed in');
  
  const { postId } = data;
  if (!postId) throw new ApiError('invalid-argument', 'postId is required');

  // Check if the user has liked this post using dynamoose
  const like = await PostLikeModel.get({
    postId: postId,
    userId: auth.uid
  });
  
  return { liked: !!like };
};

// Export the Lambda handler
export const hasUserLiked = lambdaHandler(hasUserLikedHandler);
