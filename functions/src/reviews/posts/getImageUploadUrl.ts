import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';
import { ApiError, getAuth, lambdaHandler, generateUploadUrl } from '../../common/utils/aws-helpers';

interface GetUploadUrlData {
  fileName: string;
  contentType: string;
}

interface GetUploadUrlResponse {
  uploadUrl: string;
  publicUrl: string;
}

const getImageUploadUrlHandler = async (event: APIGatewayProxyEvent, context: Context): Promise<GetUploadUrlResponse> => {
  // Parse request body
  const data: GetUploadUrlData = JSON.parse(event.body || '{}');
  const auth = getAuth(event);
  
  if (!auth) {
    throw new ApiError('unauthenticated', 'User must be authenticated.');
  }
  
  const { fileName, contentType } = data;
  if (!fileName || !contentType) {
    throw new ApiError('invalid-argument', 'fileName and contentType are required.');
  }

  const filePath = `post_images/${auth.uid}/${uuidv4()}-${fileName}`;
  
  // Generate pre-signed URL for S3 upload
  const urlInfo = await generateUploadUrl(filePath, contentType);
  
  return urlInfo;
};

// Export the Lambda handler
export const getImageUploadUrl = lambdaHandler(getImageUploadUrlHandler);
