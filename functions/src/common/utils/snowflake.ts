// src/common/utils/snowflake.ts
import FlakeId from 'flake-idgen';

const flake = new FlakeId({ epoch: 1577836800000 }); // Optional custom epoch (e.g. Jan 1, 2020)

export const generateSnowflakeId = (): string => {
  const idBuffer = flake.next(); // Buffer
  const id = BigInt('0x' + idBuffer.toString('hex')); // Convert to BigInt
  return id.toString(); // Return as string
};

//Example output id: '1234567890123456789'

