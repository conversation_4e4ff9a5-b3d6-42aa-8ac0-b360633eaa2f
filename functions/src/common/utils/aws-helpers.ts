import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, UpdateCommand, DeleteCommand, QueryCommand, ScanCommand, UpdateCommandInput } from '@aws-sdk/lib-dynamodb';
import { S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';

// Initialize AWS clients
const dynamoClient = new DynamoDBClient({});
export const docClient = DynamoDBDocumentClient.from(dynamoClient);
export const s3Client = new S3Client({});

// Environment variables from Serverless Framework
export const BUCKET_NAME = process.env.BUCKET_NAME || 'thyview-storage-dev';
export const TABLE_PREFIX = process.env.TABLE_PREFIX || 'ThyView-dev-';

// Error class to mimic Firebase's HttpsError
export class ApiError extends Error {
  statusCode: number;
  
  constructor(code: string, message: string) {
    super(message);
    this.name = 'ApiError';
    
    // Map error codes to HTTP status codes
    switch (code) {
      case 'unauthenticated':
        this.statusCode = 401;
        break;
      case 'permission-denied':
        this.statusCode = 403;
        break;
      case 'not-found':
        this.statusCode = 404;
        break;
      case 'invalid-argument':
        this.statusCode = 400;
        break;
      default:
        this.statusCode = 500;
    }
  }
}

// Extract path parameters from event
export function getPathParameters(event: APIGatewayProxyEvent): Record<string, string | undefined> {
  return event.pathParameters || {};
}

// Extract query parameters from event
export function getQueryParameters(event: APIGatewayProxyEvent): Record<string, string | undefined> {
  return event.queryStringParameters || {};
}

// Auth helper to extract user information from event
export function getAuth(event: APIGatewayProxyEvent): { uid: string } | null {
  try {
    // This is a simplification. In a real app, you'd verify the token
    // and extract user information from JWT or Cognito
    const authHeader = event.headers.Authorization || event.headers.authorization;
    if (!authHeader) return null;
    
    const token = authHeader.replace('Bearer ', '');
    // Here you would verify the token and decode user information
    // For now, let's assume token is the user ID for simplicity
    return { uid: token };
  } catch (error) {
    return null;
  }
}

// Database timestamp equivalent to Firebase's ServerTimestamp
export function serverTimestamp(): string {
  return new Date().toISOString();
}

// Helper for atomic counter increments
export async function incrementCounter(tableName: string, docId: string, field: string, amount: number = 1): Promise<any> {
  const params: UpdateCommandInput = {
    TableName: `${TABLE_PREFIX}${tableName}`,
    Key: { id: docId },
    UpdateExpression: `SET ${field} = if_not_exists(${field}, :zero) + :amount`,
    ExpressionAttributeValues: {
      ':zero': 0,
      ':amount': amount
    },
    ReturnValues: 'UPDATED_NEW'
  };
  
  return docClient.send(new UpdateCommand(params));
}

// Generate pre-signed URL for S3 uploads
export async function generateUploadUrl(filePath: string, contentType: string, expiresIn: number = 600): Promise<{uploadUrl: string; publicUrl: string}> {
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: filePath,
    ContentType: contentType
  });
  
  const url = await getSignedUrl(s3Client, command, { expiresIn });
  return {
    uploadUrl: url,
    publicUrl: `https://${BUCKET_NAME}.s3.amazonaws.com/${filePath}`
  };
}

// Helper to convert any Firebase Firestore style query params to DynamoDB format
export function convertQueryParams(params: any): any {
  // This is a simplified version - in a real app you'd need more complex conversion logic
  const convertedParams: any = {};
  
  if (params.where) {
    convertedParams.KeyConditionExpression = '';
    convertedParams.ExpressionAttributeValues = {};
    
    // Convert where clauses to KeyConditionExpression
    params.where.forEach((condition: any, index: number) => {
      const [field, operator, value] = condition;
      const placeholder = `:val${index}`;
      
      if (index > 0) {
        convertedParams.KeyConditionExpression += ' AND ';
      }
      
      switch (operator) {
        case '==':
          convertedParams.KeyConditionExpression += `${field} = ${placeholder}`;
          break;
        case '>':
          convertedParams.KeyConditionExpression += `${field} > ${placeholder}`;
          break;
        case '>=':
          convertedParams.KeyConditionExpression += `${field} >= ${placeholder}`;
          break;
        case '<':
          convertedParams.KeyConditionExpression += `${field} < ${placeholder}`;
          break;
        case '<=':
          convertedParams.KeyConditionExpression += `${field} <= ${placeholder}`;
          break;
      }
      
      convertedParams.ExpressionAttributeValues[placeholder] = value;
    });
  }
  
  if (params.orderBy) {
    convertedParams.ScanIndexForward = params.orderBy[1] !== 'desc';
  }
  
  if (params.limit) {
    convertedParams.Limit = params.limit;
  }
  
  return convertedParams;
}

// Lambda handler wrapper to standardize response format for REST APIs
export function lambdaHandler(handler: (event: APIGatewayProxyEvent, context: Context) => Promise<any>) {
  return async (event: APIGatewayProxyEvent, context: Context) => {
    try {
      // Parse the request body if it exists
      let data = {};
      if (event.body) {
        data = JSON.parse(event.body);
      }
      
      // Get parameters
      const pathParams = getPathParameters(event);
      const queryParams = getQueryParameters(event);
      
      // Get auth information
      const auth = getAuth(event);
      
      // Call the actual handler
      const result = await handler(event, context);
      
      // Return a standardized response
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify(result)
      };
    } catch (error) {
      // Handle errors
      console.error('Error:', error);
      
      let statusCode = 500;
      let message = 'Internal server error';
      
      if (error instanceof ApiError) {
        statusCode = error.statusCode;
        message = error.message;
      }
      
      return {
        statusCode,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({
          error: message
        })
      };
    }
  };
}
