// src/utils/logger.ts
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

interface LogContext {
  [key: string]: unknown;
}

export class Logger {
  private static instance: Logger;
  private logLevel: LogLevel;
  private serviceName: string;

  private constructor(serviceName: string = 'app', logLevel: LogLevel = LogLevel.INFO) {
    this.serviceName = serviceName;
    this.logLevel = process.env.LOG_LEVEL ? 
      LogLevel[process.env.LOG_LEVEL as keyof typeof LogLevel] ?? LogLevel.INFO : 
      logLevel;
  }

  public static getInstance(serviceName?: string, logLevel?: LogLevel): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(serviceName, logLevel);
    }
    return Logger.instance;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel;
  }

  private formatMessage(level: string, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      service: this.serviceName,
      message,
      ...(context && { context })
    };
    return JSON.stringify(logEntry);
  }

  public debug(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.debug(this.formatMessage('DEBUG', message, context));
    }
  }

  public info(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.INFO)) {
      console.info(this.formatMessage('INFO', message, context));
    }
  }

  public warn(message: string, context?: LogContext): void {
    if (this.shouldLog(LogLevel.WARN)) {
      console.warn(this.formatMessage('WARN', message, context));
    }
  }

  public error(message: string, error?: Error | unknown, context?: LogContext): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      const errorContext: LogContext = {
        ...context
      };
      
      if (error instanceof Error) {
        errorContext.error = {
          name: error.name,
          message: error.message,
          stack: error.stack
        };
      } else if (error) {
        errorContext.error = error;
      }
      
      console.error(this.formatMessage('ERROR', message, errorContext));
    }
  }

  public setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }
}