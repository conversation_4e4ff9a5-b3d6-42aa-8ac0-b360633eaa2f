// Original file: functions/src/global_shared.ts
// Moved to: functions/src/utils/global-shared.ts
// Note: Contents remain the same but naming convention is changed to kebab-case

// This file would contain global shared constants and utilities
// Since we don't have the original content, this is a placeholder.
// When implementing, copy the content from the original file.
import { APIGatewayProxyEvent } from 'aws-lambda';
import { ApiError, getAuth } from '../utils/aws-helpers';

export const TABLE_PREFIX = process.env.TABLE_PREFIX || 'ThyView-dev-';
export const BUCKET_NAME = process.env.BUCKET_NAME || 'thyview-storage-dev';

export const TABLES = {
  POSTS: `${TABLE_PREFIX}posts`,
  POST_LIKES: `${TABLE_PREFIX}post_likes`,
  COMMENTS: `${TABLE_PREFIX}comments`,
  COMMENT_LIKES: `${TABLE_PREFIX}comment_likes`,
  POST_REPORTS: `${TABLE_PREFIX}post_reports`,
  COMMENT_REPORTS: `${TABLE_PREFIX}comment_reports`,
  USERS: `${TABLE_PREFIX}users`
};

export const isProduction = () => {
  return process.env.NODE_ENV === 'production';
};


export const DYNAMODB_CONFIG = {
  local: {
    endpoint: 'http://localhost:8000',
    region: 'us-east-1',
    credentials: {
      accessKeyId: 'local',
      secretAccessKey: 'local'
    }
  },
  production: {
    region: 'us-east-1'
  }
};

export const IS_LOCAL = process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development';

export const getDynamoDBConfig = () => IS_LOCAL ? DYNAMODB_CONFIG.local : DYNAMODB_CONFIG.production;