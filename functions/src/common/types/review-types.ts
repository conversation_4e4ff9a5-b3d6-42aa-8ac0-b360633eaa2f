// Common type definitions for the entire application

// Post-related types
export interface Post {
  id: string;
  externalAuthorId: string;
  text: string;
  imageUrl?: string | null;
  likeCount: number;
  commentCount: number;
  createdAt: string;
  liked: boolean;
  username?: string;
  profileImageUrl?: string | null;
  updatedAt?: string;
}

// Comment-related types
export interface Comment {
  id: string;
  postId: string;
  externalAuthorId: string;
  text: string;
  parentCommentId: string | null;
  imageUrl?: string | null;
  createdAt: string;
  updatedAt?: string;
  likeCount?: number;
  replyCount?: number;
}

export interface Reply extends Comment {
  username: string;
  liked: boolean;
}

// User-related types
export interface User {
  id: string;
  externalAuthorId: string;
  username: string;
  profileImageUrl?: string | null;
  email?: string;
  createdAt: string;
}

// Like-related types
export interface PostLike {
  postId: string;
  userId: string;
  likedAt: string;
}

export interface CommentLike {
  commentId: string;
  userId: string;
  postId: string;  // For cross-reference
  likedAt: string;
}

// Report-related types
export interface PostReport {
  id: string;
  postId: string;
  userId: string;
  reason: string;
  reportedAt: string;
}

export interface CommentReport {
  id: string;
  commentId: string;
  postId: string;
  userId: string;
  reason: string;
  reportedAt: string;
}

// API response types for consistent error handling
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
  };
}
