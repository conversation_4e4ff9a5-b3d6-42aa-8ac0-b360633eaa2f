// src/articles/articles-types.ts
export interface Article {
  id: string;
  title: string;
  description: string;
  imageUrl?: string;
  contentUrl?: string;
  likes: number;
  createdAt: string;
  modifiedAt: string;
  postedBy: string; // externalAuthorId
  additionalNotes?: string;
  content?: string;
  tags?: string[];
}

// Article with like status
export interface ArticleWithLikeStatus extends Article {
  isLikedByUser: boolean;
}

export interface ArticleLike {
  articleId: string;
  userId: string;
  createdAt: string;
}

// Request types
export interface CreateArticleRequest {
  title: string;
  description: string;
  imageUrl?: string;
  contentUrl?: string;
  postedBy: string;
  additionalNotes?: string;
  content?: string;
  tags?: string[];
}

export interface UpdateArticleRequest {
  id: string;
  title?: string;
  description?: string;
  imageUrl?: string;
  contentUrl?: string;
  additionalNotes?: string;
  content?: string;
  tags?: string[];
}

export interface LikeArticleRequest {
  articleId: string;
  userId: string;
}

// Response types
export interface ArticlesApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  nextKey?: string;
  totalCount?: number;
}

// Query types
export interface GetArticlesQuery {
  limit?: number;
  lastKey?: string;
  postedBy?: string;
  tags?: string[];
  sortBy?: 'createdAt' | 'likes';
  order?: 'asc' | 'desc';
}

export interface GetLikedArticlesQuery {
  userId: string;
  limit?: number;
  lastKey?: string;
}

// Article liker type
export interface ArticleLiker {
  userId: string;
  createdAt: string;
}

export interface ArticleLikersResponse {
  items: ArticleLiker[];
  nextKey?: string;
  totalLikes: number;
}// src/common/types/articles-types.ts
export interface Article {
  id: string;
  title: string;
  description: string;
  imageUrl?: string;
  contentUrl?: string;
  likes: number;
  createdAt: string;
  modifiedAt: string;
  postedBy: string; // externalAuthorId
  additionalNotes?: string;
  content?: string;
  tags?: string[];
}

// Article with like status
export interface ArticleWithLikeStatus extends Article {
  isLikedByUser: boolean;
}

export interface ArticleLike {
  articleId: string;
  userId: string;
  createdAt: string;
}

// Request types
export interface CreateArticleRequest {
  title: string;
  description: string;
  imageUrl?: string;
  contentUrl?: string;
  postedBy: string;
  additionalNotes?: string;
  content?: string;
  tags?: string[];
}

export interface UpdateArticleRequest {
  id: string;
  title?: string;
  description?: string;
  imageUrl?: string;
  contentUrl?: string;
  additionalNotes?: string;
  content?: string;
  tags?: string[];
}

export interface LikeArticleRequest {
  articleId: string;
  userId: string;
}

// Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  nextKey?: string;
  totalCount?: number;
}

// Query types
export interface GetArticlesQuery {
  limit?: number;
  lastKey?: string;
  postedBy?: string;
  tags?: string[];
  sortBy?: 'createdAt' | 'likes';
  order?: 'asc' | 'desc';
}

export interface GetLikedArticlesQuery {
  userId: string;
  limit?: number;
  lastKey?: string;
}

// Article liker type
export interface ArticleLiker {
  userId: string;
  createdAt: string;
}

export interface ArticleLikersResponse {
  items: ArticleLiker[];
  nextKey?: string;
  totalLikes: number;
}