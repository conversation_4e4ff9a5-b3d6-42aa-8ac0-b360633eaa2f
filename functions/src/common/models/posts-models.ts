import * as dynamoose from 'dynamoose';
import { Item } from 'dynamoose/dist/Item';
import { TABLE_PREFIX } from '../../common/utils/aws-helpers';

/**
 * Interface representing a Post document in DynamoDB
 */
export interface PostDocument extends Item {
  id: string;
  authorUid: string;
  externalAuthorId: string;
  username: string;
  profileImageUrl: string | null;
  text: string;
  imageUrl: string | null;
  likeCount: number;
  commentCount: number;
  createdAt: number;
  updatedAt: number;
}

const postSchema = new dynamoose.Schema({
  id: {
    type: String,
    hashKey: true,
    required: true
  },
  authorUid: {
    type: String,
    required: true,
    index: {
      name: 'AuthorUidIndex',
      type: 'global'
    }
  },
  externalAuthorId: {
    type: String,
    required: true,
    index: {
      name: 'ExternalAuthorIdIndex',
      type: 'global'
    }
  },
  username: {
    type: String,
    required: true
  },
  profileImageUrl: String,
  text: {
    type: String,
    required: true
  },
  imageUrl: String,
  likeCount: {
    type: Number,
    default: 0
  },
  commentCount: {
    type: Number,
    default: 0
  },
  createdAt: {
    type: Number,
    required: true,
    index: {
      name: 'CreatedAtIndex',
      type: 'global'
    }
  },
  updatedAt: {
    type: Number,
    required: true
  }
}, {
  timestamps: false
});

export const PostModel = dynamoose.model<PostDocument>(
  `${TABLE_PREFIX}posts`,
  postSchema
);

/**
 * Interface representing a PostLike document in DynamoDB
 */
export interface PostLikeDocument extends Item {
  postId: string;
  userId: string;
  likedAt: number;
}

const postLikeSchema = new dynamoose.Schema({
  postId: {
    type: String,
    hashKey: true,
    required: true
  },
  userId: {
    type: String,
    rangeKey: true,
    required: true
  },
  likedAt: {
    type: Number,
    required: true
  }
}, {
  timestamps: false
});

export const PostLikeModel = dynamoose.model<PostLikeDocument>(
  `${TABLE_PREFIX}post_likes`,
  postLikeSchema
);
