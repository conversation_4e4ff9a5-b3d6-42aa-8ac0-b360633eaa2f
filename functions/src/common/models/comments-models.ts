import * as dynamoose from 'dynamoose';
import { Item } from 'dynamoose/dist/Item';
import { TABLE_PREFIX } from '../../common/utils/aws-helpers';

/**
 * Interface representing a Comment document in DynamoDB
 */
export interface CommentDocument extends Item {
  id: string;
  postId: string;
  externalAuthorId: string;
  text: string;
  parentCommentId: string; // 'null' stored as string for consistency
  imageUrl: string | null;
  likeCount: number;
  replyCount: number;
  createdAt: number;
  updatedAt?: number;
  username?: string; // Added username field
}

const commentSchema = new dynamoose.Schema({
  id: {
    type: String,
    hashKey: true,
    required: true
  },
  postId: {
    type: String,
    required: true,
    index: {
      name: 'PostIdCreatedAtIndex',
      type: 'global',
      rangeKey: 'createdAt'
    }
  },
  externalAuthorId: {
    type: String,
    required: true
  },
  text: {
    type: String,
    required: true
  },
  parentCommentId: {
    type: String,
    default: 'null', // Store null as string 'null' for consistency
    index: {
      name: 'PostIdParentCommentIndex',
      type: 'global',
      rangeKey: 'postId'
    }
  },
  imageUrl: String,
  likeCount: {
    type: Number,
    default: 0
  },
  replyCount: {
    type: Number,
    default: 0
  },
  createdAt: {
    type: Number,
    required: true
  },
  updatedAt: {
    type: Number,
    required: false
  },
  username: {
    type: String,
    required: false
  }
}, {
  timestamps: false
});

export const CommentModel = dynamoose.model<CommentDocument>(
  `${TABLE_PREFIX}comments`,
  commentSchema
);

/**
 * Interface representing a CommentLike document in DynamoDB
 */
export interface CommentLikeDocument extends Item {
  commentId: string;
  userId: string;
  postId: string;
  likedAt: number;
}

const commentLikeSchema = new dynamoose.Schema({
  commentId: {
    type: String,
    hashKey: true,
    required: true
  },
  userId: {
    type: String,
    rangeKey: true,
    required: true
  },
  postId: {
    type: String,
    required: true
  },
  likedAt: {
    type: Number,
    required: true
  }
}, {
  timestamps: false
});

export const CommentLikeModel = dynamoose.model<CommentLikeDocument>(
  `${TABLE_PREFIX}comment_likes`,
  commentLikeSchema
);
