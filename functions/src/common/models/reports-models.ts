import * as dynamoose from 'dynamoose';
import { Item } from 'dynamoose/dist/Item';
import { TABLE_PREFIX } from '../../common/utils/aws-helpers';

/**
 * Interface representing a PostReport document in DynamoDB
 */
export interface PostReportDocument extends Item {
  id: string;
  postId: string;
  userId: string;
  reason: string;
  reportedAt: number;
}

const postReportSchema = new dynamoose.Schema({
  id: {
    type: String,
    hashKey: true,
    required: true
  },
  postId: {
    type: String,
    required: true,
    index: {
      name: 'PostIdIndex',
      type: 'global'
    }
  },
  userId: {
    type: String,
    required: true
  },
  reason: {
    type: String,
    required: true
  },
  reportedAt: {
    type: Number,
    required: true
  }
}, {
  timestamps: false
});

export const PostReportModel = dynamoose.model<PostReportDocument>(
  `${TABLE_PREFIX}post_reports`,
  postReportSchema
);

/**
 * Interface representing a CommentReport document in DynamoDB
 */
export interface CommentReportDocument extends Item {
  id: string;
  commentId: string;
  postId: string;
  userId: string;
  reason: string;
  reportedAt: number;
}

const commentReportSchema = new dynamoose.Schema({
  id: {
    type: String,
    hashKey: true,
    required: true
  },
  commentId: {
    type: String,
    required: true,
    index: {
      name: 'CommentIdIndex',
      type: 'global'
    }
  },
  postId: {
    type: String,
    required: true
  },
  userId: {
    type: String,
    required: true
  },
  reason: {
    type: String,
    required: true
  },
  reportedAt: {
    type: Number,
    required: true
  }
}, {
  timestamps: false
});

export const CommentReportModel = dynamoose.model<CommentReportDocument>(
  `${TABLE_PREFIX}comment_reports`,
  commentReportSchema
);
