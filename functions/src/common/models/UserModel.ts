import dynamoose, { Schema } from 'dynamoose';

export interface IUser {
  id: string;
  username?: string;
  email?: string;
  name?: string;
  createdAt?: string;
  updatedAt?: string;
  profileImageUrl?: string;
  bio?: string;
  preferredLanguages?: string[];
  favoriteGenres?: string[];
  fans?: number;
  fanned?: number;
  reviewCount?: number;
  averageRating?: number;
  watchListCount?: number;
  region?: string;
  isPrivate?: boolean;
  externalUserId?: string;
  role?: string;
}


const userSchema = new Schema({
  id: {
    type: String,
    hashKey: true,
    required: true,
  },
  username: {
    type: String,
    index: {
      type: 'global',
      name: 'username-index',
    },
  },
  email: {
    type: String,
    index: {
      type: 'global',
      name: 'email-index',
    },
  },
  name: String,
  profileImageUrl: String,
  bio: String,
  preferredLanguages: {
    type: Array,
    schema: [String],
  },
  favoriteGenres: {
    type: Array,
    schema: [String],
  },
  fans: {
    type: Number,
    default: 0,
  },
  fanned: {
    type: Number,
    default: 0,
  },
  reviewCount: {
    type: Number,
    default: 0,
  },
  averageRating: {
    type: Number,
    default: 0,
  },
  watchListCount: {
    type: Number,
    default: 0,
  },
  region: String,
  isPrivate: {
    type: Boolean,
    default: false,
  },
  externalUserId: {
    type: String,
    index: {
      type: 'global',
      name: 'externalUserId-index',
    },
  },
  role: {
    type: String,
    default: 'user',
  },
}, {
  timestamps: true, // This automatically creates createdAt and updatedAt
});

export const UserModel = dynamoose.model(`users-${process.env.STAGE}`, userSchema);