import * as dynamoose from 'dynamoose';
import { Item } from 'dynamoose/dist/Item';

/**
 * Interface representing a User document in DynamoDB
 */
export interface UserDocument extends Item {
  userId: string;
  id: string;
  email: string;
  displayName: string;
  photoURL?: string;
  createdAt: number;
  updatedAt: number;
  bio?: string;
  location?: string;
  website?: string;
  profession?: string;
  interests?: string[];
  isVerified: boolean;
  externalAuthorId: string;
  profileImageUrl?: string;
  username?: string;
  role?: string;
}

const userSchema = new dynamoose.Schema({
  userId: {
    type: String,
    hashKey: true,
    required: true
  },
  email: {
    type: String,
    required: true,
    index: {
      name: 'emailIndex',
      type: 'global'
    }
  },
  displayName: {
    type: String,
    required: true
  },
  photoURL: String,
  createdAt: {
    type: Number,
    required: true
  },
  updatedAt: {
    type: Number,
    required: true
  },
  bio: String,
  location: String,
  website: String,
  profession: String,
  interests: {
    type: Array,
    schema: [String]
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  id: {
    type: String,
    required: true
  },
  externalAuthorId: {
    type: String,
    required: true
  },
  profileImageUrl: String,
  username: String,
  role: {
    type: String,
    default: 'user'
  }
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  }
});

export const UserModel = dynamoose.model<UserDocument>(
  'Users',
  userSchema
);
