import * as cdk from 'aws-cdk-lib';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import { Construct } from 'constructs';

export class DynamoDBTables extends Construct {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id);

    // Environment name (dev, prod)
    const env = this.node.tryGetContext('env') || 'dev';
    const tablePrefix = `ThyView-${env}-`;

    // Posts Table
    const postsTable = new dynamodb.Table(this, 'PostsTable', {
      tableName: `${tablePrefix}posts`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: env === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: env === 'prod',
    });
    
    // Add GSI for createdAt to support efficient date-based queries
    postsTable.addGlobalSecondaryIndex({
      indexName: 'CreatedAtIndex',
      partitionKey: { name: 'authorUid', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'createdAt', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });

    // Post Likes Table
    const postLikesTable = new dynamodb.Table(this, 'PostLikesTable', {
      tableName: `${tablePrefix}post_likes`,
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: env === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: env === 'prod',
    });

    // Comments Table
    const commentsTable = new dynamodb.Table(this, 'CommentsTable', {
      tableName: `${tablePrefix}comments`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: env === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: env === 'prod',
    });
    
    // Add GSI for postId + parentCommentId to support queries for comments on a post
    commentsTable.addGlobalSecondaryIndex({
      indexName: 'PostIdParentCommentIndex',
      partitionKey: { name: 'postId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'parentCommentId', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });

    // Comment Likes Table
    const commentLikesTable = new dynamodb.Table(this, 'CommentLikesTable', {
      tableName: `${tablePrefix}comment_likes`,
      partitionKey: { name: 'commentId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: env === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: env === 'prod',
    });

    // Post Reports Table
    const postReportsTable = new dynamodb.Table(this, 'PostReportsTable', {
      tableName: `${tablePrefix}post_reports`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: env === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: env === 'prod',
    });

    // Comment Reports Table
    const commentReportsTable = new dynamodb.Table(this, 'CommentReportsTable', {
      tableName: `${tablePrefix}comment_reports`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: env === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: env === 'prod',
    });

    // Users Table
    const usersTable = new dynamodb.Table(this, 'UsersTable', {
      tableName: `${tablePrefix}users`,
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: env === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
      pointInTimeRecovery: env === 'prod',
    });

    // Add GSI for externalAuthorId to support lookups by external ID
    usersTable.addGlobalSecondaryIndex({
      indexName: 'ExternalAuthorIdIndex',
      partitionKey: { name: 'externalAuthorId', type: dynamodb.AttributeType.STRING },
      projectionType: dynamodb.ProjectionType.ALL,
    });
  }
}
