import * as cdk from 'aws-cdk-lib';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';

export interface ApiGatewayProps {
  lambdaFunctions: {
    [key: string]: lambda.Function;
  };
}

export class ApiGatewayConstruct extends Construct {
  public readonly api: apigateway.RestApi;

  constructor(scope: Construct, id: string, props: ApiGatewayProps) {
    super(scope, id);

    // Environment name (dev, prod)
    const env = this.node.tryGetContext('env') || 'dev';

    // Create API Gateway
    this.api = new apigateway.RestApi(this, 'ThyViewApi', {
      restApiName: `thyview-api-${env}`,
      description: 'API Gateway for ThyView services',
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS, // In production, restrict to your domains
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: ['Content-Type', 'Authorization', 'X-Amz-Date', 'X-Api-Key', 'X-Amz-Security-Token'],
        maxAge: cdk.Duration.days(1),
      },
      deployOptions: {
        stageName: env,
        loggingLevel: apigateway.MethodLoggingLevel.INFO,
        dataTraceEnabled: env !== 'prod', // Enable detailed logging in non-prod
      },
    });

    // Create API resources and methods
    this.createPostsResources(props.lambdaFunctions);
    this.createCommentsResources(props.lambdaFunctions);
    this.createReportsResources(props.lambdaFunctions);
    this.createLoggerResource(props.lambdaFunctions);

    // Add API Gateway documentation if needed
    if (env !== 'dev') {
      this.addDocumentation();
    }
  }

  private createPostsResources(lambdaFunctions: { [key: string]: lambda.Function }) {
    const postsResource = this.api.root.addResource('posts');

    // POST /posts/create
    const createPostResource = postsResource.addResource('create');
    createPostResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.createPost));

    // POST /posts/edit
    const editPostResource = postsResource.addResource('edit');
    editPostResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.editPost));

    // POST /posts/delete
    const deletePostResource = postsResource.addResource('delete');
    deletePostResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.deletePost));

    // POST /posts/like
    const likePostResource = postsResource.addResource('like');
    likePostResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.likePost));

    // POST /posts/hasLiked
    const hasLikedResource = postsResource.addResource('hasLiked');
    hasLikedResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.hasUserLiked));

    // POST /posts/list
    const listPostsResource = postsResource.addResource('list');
    listPostsResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.getPostsList));

    // POST /posts/detail
    const postDetailResource = postsResource.addResource('detail');
    postDetailResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.getPostDetail));

    // POST /posts/getUploadUrl
    const uploadUrlResource = postsResource.addResource('getUploadUrl');
    uploadUrlResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.getImageUploadUrl));
  }

  private createCommentsResources(lambdaFunctions: { [key: string]: lambda.Function }) {
    const commentsResource = this.api.root.addResource('comments');

    // POST /comments/add
    const addCommentResource = commentsResource.addResource('add');
    addCommentResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.addComment));

    // POST /comments/submit
    const submitCommentResource = commentsResource.addResource('submit');
    submitCommentResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.submitComment));

    // POST /comments/list
    const getCommentsResource = commentsResource.addResource('list');
    getCommentsResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.getComments));

    // POST /comments/replies
    const getRepliesResource = commentsResource.addResource('replies');
    getRepliesResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.getReplies));

    // POST /comments/like
    const likeCommentResource = commentsResource.addResource('like');
    likeCommentResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.likeComment));

    // POST /comments/delete
    const deleteCommentResource = commentsResource.addResource('delete');
    deleteCommentResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.deleteComment));
  }

  private createReportsResources(lambdaFunctions: { [key: string]: lambda.Function }) {
    const reportsResource = this.api.root.addResource('reports');

    // POST /reports/post
    const reportPostResource = reportsResource.addResource('post');
    reportPostResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.reportPost));

    // POST /reports/comment
    const reportCommentResource = reportsResource.addResource('comment');
    reportCommentResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.reportComment));
  }

  private createLoggerResource(lambdaFunctions: { [key: string]: lambda.Function }) {
    // POST /log
    const logResource = this.api.root.addResource('log');
    logResource.addMethod('POST', new apigateway.LambdaIntegration(lambdaFunctions.appToCloudLogger));
  }

  private addDocumentation() {
    // Add documentation for the API
    const documentationVersion = 'v1';
    const apiGatewayDocumentation = new apigateway.CfnDocumentationVersion(
      this,
      'ApiGatewayDocumentation',
      {
        restApiId: this.api.restApiId,
        documentationVersion,
        description: 'ThyView API Documentation',
      }
    );
  }
}
