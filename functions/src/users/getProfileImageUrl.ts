import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { successResponse, errorResponse } from '../common/utils/response';
import { Logger } from '../common/utils/logger';

const logger = Logger.getInstance('getProfileImageUrl');

const BUCKET_NAME = "thyview-profile-pictures-stage";
const EXPIRES_IN_SECONDS = 7 * 24 * 60 * 60; // 7 days

const s3Client = new S3Client({});

export const getProfileImageUrl = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    const uid = (event.requestContext.authorizer as any)?.uid;

    if (!uid) {
      logger.warn('Missing UID from authorizer context');
      return errorResponse(401, 'Unauthorized');
    }

    const key = `profile_pictures/${uid}/profile_picture.jpg`;

    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    });

    const signedUrl = await getSignedUrl(s3Client, command, {
      expiresIn: EXPIRES_IN_SECONDS,
    });

    return successResponse({ url: signedUrl });
  } catch (err) {
    logger.error('Failed to generate presigned GET URL', err);
    return errorResponse(500, 'Failed to generate image URL');
  }
};
