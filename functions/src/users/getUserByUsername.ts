import { APIGatewayProxyHandlerV2 } from 'aws-lambda';
import { UserModel } from '../common/models/UserModel';
import { successResponse, errorResponse } from '../common/utils/response';
import { Logger } from '../common/utils/logger';

const logger = Logger.getInstance('getUserByUsername');

export const getUserByUsername: APIGatewayProxyHandlerV2 = async (event) => {
  try {
    const username = event.queryStringParameters?.username;

    if (!username) {
      return errorResponse(400, 'Username is required.');
    }

    const result = await UserModel.query('username')
      .eq(username)
      .exec();

    const user = result[0];

    if (!user) {
      return errorResponse(404, 'User not found.');
    }

    return successResponse(user);
  } catch (error) {
    logger.error('Get user by username failed', error);
    return errorResponse(500, 'Internal server error.');
  }
};
