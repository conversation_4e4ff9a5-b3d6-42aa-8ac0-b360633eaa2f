import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { UserModel } from '../common/models/UserModel';
import { successResponse, errorResponse } from '../common/utils/response';
import { Logger } from '../common/utils/logger';

const logger = Logger.getInstance('deleteUser');

export const deleteUser = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const firebaseUid = event.requestContext.authorizer?.uid;

    if (!firebaseUid) {
      return errorResponse(400, 'Bad Request: Firebase UID missing.');
    }

    await UserModel.delete(firebaseUid);
    logger.info('User deleted', { id: firebaseUid });

    return successResponse({ message: 'User deleted successfully.' });
  } catch (error) {
    logger.error('Delete user failed', error);
    return errorResponse(500, 'Internal server error.');
  }
};
