// src/users/getPresignedUrl.ts
import {
  S3Client,
  PutObjectCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { successResponse, errorResponse } from '../common/utils/response';
import { Logger } from '../common/utils/logger';

const logger = Logger.getInstance('getPresignedUrl');

const s3Client = new S3Client({ region: process.env.AWS_REGION || 'us-east-1' });

export const getPresignedUrl = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  try {
    const uid = (event.requestContext.authorizer as any)?.uid;
    if (!uid) {
      logger.warn('Missing UID in request context');
      return errorResponse(401, 'Unauthorized');
    }

    const fileName = `profile_pictures/${uid}/profile_picture.jpg`;
    const command = new PutObjectCommand({
      Bucket: "thyview-profile-pictures-stage",
      Key: fileName,
      ContentType: 'image/jpeg',
    });

    const signedUrl = await getSignedUrl(s3Client, command, {
      expiresIn: 300, // 5 minutes
    });

    logger.info('Presigned URL generated', { uid, key: fileName });

    return successResponse({ url: signedUrl, key: fileName });
  } catch (error) {
    logger.error('Presigned URL generation failed', error);
    return errorResponse(500, 'Could not generate presigned URL');
  }
};
