// functions/src/users/updateUser.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { UserModel } from '../common/models/UserModel';
import { successResponse, errorResponse } from '../common/utils/response';
import { Logger } from '../common/utils/logger';

const logger = Logger.getInstance('updateUser');

interface UpdateUserRequest {
  username?: string;
  email?: string;
  name?: string;
  profileImageUrl?: string;
  bio?: string;
  preferredLanguages?: string[];
  favoriteGenres?: string[];
  fans?: number;
  fanned?: number;
  reviewCount?: number;
  averageRating?: number;
  watchListCount?: number;
  region?: string;
  isPrivate?: boolean;
}

export const updateUser = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const firebaseUid = event.requestContext?.authorizer?.uid;
    if (!firebaseUid) return errorResponse(400, 'Bad Request: Firebase UID missing.');

    const body: UpdateUserRequest = JSON.parse(event.body || '{}');
    const user = await UserModel.get(firebaseUid);

    if (!user) return errorResponse(404, 'User not found.');

    // Check for username conflict
    if (body.username && body.username !== user.username) {
      const existing = await UserModel.query('username').eq(body.username).limit(1).exec();
      if (existing.count > 0) return errorResponse(409, 'Username is already taken.');
      user.username = body.username;
    }

    // Update other fields
    if (body.email !== undefined) user.email = body.email;
    if (body.name !== undefined) user.name = body.name;
    if (body.profileImageUrl !== undefined) user.profileImageUrl = body.profileImageUrl;
    if (body.bio !== undefined) user.bio = body.bio;
    if (body.preferredLanguages !== undefined) user.preferredLanguages = body.preferredLanguages;
    if (body.favoriteGenres !== undefined) user.favoriteGenres = body.favoriteGenres;
    if (body.fans !== undefined) user.fans = body.fans;
    if (body.fanned !== undefined) user.fanned = body.fanned;
    if (body.reviewCount !== undefined) user.reviewCount = body.reviewCount;
    if (body.averageRating !== undefined) user.averageRating = body.averageRating;
    if (body.watchListCount !== undefined) user.watchListCount = body.watchListCount;
    if (body.region !== undefined) user.region = body.region;
    if (body.isPrivate !== undefined) user.isPrivate = body.isPrivate;

    const updatedUser = await user.save();

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(updatedUser),
    };
  } catch (error) {
    console.error('Error updating user:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({ error: 'Failed to update user' }),
    };
  }
};
