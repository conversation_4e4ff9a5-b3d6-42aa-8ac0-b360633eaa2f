import { APIGatewayProxyHandlerV2 } from 'aws-lambda';
import { UserModel } from '../common/models/UserModel';
import { successResponse, errorResponse } from '../common/utils/response';
import { Logger } from '../common/utils/logger';

const logger = Logger.getInstance('checkUsernameAvailability');

export const checkUsernameAvailability: APIGatewayProxyHandlerV2 = async (event) => {
  const { username } = event.queryStringParameters || {};

  if (!username) {
    return errorResponse(400, 'Username query parameter is required.');
  }

  try {
    const result = await UserModel.query('username').eq(username).limit(1).exec();
    const available = result.count === 0;
    return successResponse({ available });
  } catch (error) {
    logger.error('Check username availability failed', error);
    return errorResponse(500, 'Internal server error.');
  }
};
