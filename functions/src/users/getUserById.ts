import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { UserModel } from '../common/models/UserModel';
import { successResponse, errorResponse } from '../common/utils/response';
import { Logger } from '../common/utils/logger';

const logger = Logger.getInstance('getUserById');

export const getUserById = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const queryParams = event.queryStringParameters || {};
    const externalUserId = queryParams.externalUserId;
    const firebaseUid = event.requestContext.authorizer?.uid;

    if (!firebaseUid && !externalUserId) {
      return errorResponse(400, 'Either Firebase UID or externalUserId is required.');
    }

    let user;

    if (externalUserId) {
      const results = await UserModel.query('externalUserId')
        .eq(externalUserId)
        .exec();

      user = results[0];
    } else {
      user = await UserModel.get(firebaseUid);
    }

    if (!user) {
      return errorResponse(404, 'User not found.');
    }

    return successResponse(user);
  } catch (error) {
    logger.error('Get user failed', error);
    return errorResponse(500, 'Internal server error.');
  }
};
