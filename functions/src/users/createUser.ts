// functions/src/users/createUser.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { UserModel } from '../common/models/UserModel';
import { generateSnowflakeId } from '../common/utils/snowflake';
import { successResponse, errorResponse } from '../common/utils/response';
import { Logger } from '../common/utils/logger';

const logger = Logger.getInstance('createUser');

interface CreateUserRequest {
  username?: string;
  email?: string;
  name?: string;
  profileImageUrl?: string;
  bio?: string;
  preferredLanguages?: string[];
  favoriteGenres?: string[];
  region?: string;
  isPrivate?: boolean;
}

export const createUser = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const firebaseUid = event.requestContext?.authorizer?.uid;
    if (!firebaseUid) return errorResponse(400, 'Bad Request: Firebase UID missing.');

    const body: CreateUserRequest = JSON.parse(event.body || '{}');

    // Check if username is taken
    if (body.username) {
      const existing = await UserModel.query('username').eq(body.username).limit(1).exec();
      if (existing.count > 0) return errorResponse(409, 'Username is already taken.');
    }

    const newUser = new UserModel({
      id: firebaseUid,
      externalUserId: generateSnowflakeId(),
      username: body.username,
      email: body.email,
      name: body.name,
      profileImageUrl: body.profileImageUrl,
      bio: body.bio,
      preferredLanguages: body.preferredLanguages || [],
      favoriteGenres: body.favoriteGenres || [],
      fans: 0,
      fanned: 0,
      reviewCount: 0,
      averageRating: 0,
      watchListCount: 0,
      region: body.region,
      isPrivate: body.isPrivate ?? false
      // role will be set to default 'user' by the schema
    });

    const savedUser = await newUser.save();

    return {
      statusCode: 201,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify(savedUser)
    };
  } catch (error) {
    console.error('Error creating user:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({ error: 'Failed to create user' })
    };
  }
};
