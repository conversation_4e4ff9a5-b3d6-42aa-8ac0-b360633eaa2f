// src/articles/articles-models.ts
import * as dynamoose from 'dynamoose';
import { Item } from 'dynamoose/dist/Item';
import { 
  Article, 
  ArticleLike
} from '../common/types/articles-types';

// Article Schema
const articleSchema = new dynamoose.Schema({
  id: {
    type: String,
    hashKey: true,
    required: true,
  },
  title: {
    type: String,
    required: true,
    index: {
      name: 'title-index',
      type: 'global',
    },
  },
  description: {
    type: String,
    required: true,
  },
  imageUrl: {
    type: String,
    required: false,
  },
  contentUrl: {
    type: String,
    required: false,
  },
  likes: {
    type: Number,
    required: true,
    default: 0,
    index: {
      name: 'likes-index',
      type: 'global',
      rangeKey: 'createdAt',
    },
  },
  createdAt: {
    type: String,
    required: true,
    index: {
      name: 'createdAt-index',
      type: 'global',
    },
  },
  modifiedAt: {
    type: String,
    required: true,
  },
  postedBy: {
    type: String,
    required: true,
    index: {
      name: 'postedBy-index',
      type: 'global',
      rangeKey: 'createdAt',
    },
  },
  additionalNotes: {
    type: String,
    required: false,
  },
  content: {
    type: String,
    required: false,
  },
  tags: {
    type: Array,
    schema: [String],
    required: false,
    index: {
      name: 'tags-index',
      type: 'global',
    },
  },
}, {
  timestamps: false,
});

// Article Like Schema
const articleLikeSchema = new dynamoose.Schema({
  id: {
    type: String,
    hashKey: true,
    required: true,
  },
  articleId: {
    type: String,
    required: true,
    index: {
      name: 'articleId-index',
      type: 'global',
      rangeKey: 'createdAt',
    },
  },
  userId: {
    type: String,
    required: true,
    index: {
      name: 'userId-index',
      type: 'global',
      rangeKey: 'createdAt',
    },
  },
  createdAt: {
    type: String,
    required: true,
  },
}, {
  timestamps: false,
});

// Models
export interface ArticleDocument extends Item, Article {}
export interface ArticleLikeDocument extends Item, ArticleLike {
  id: string;
}

// Dynamically construct table name with prefix from env
const tablePrefix = process.env.TABLE_PREFIX || 'ThyView-dev-';

export const ArticleModel = dynamoose.model<ArticleDocument>(
  `${tablePrefix}articles`, 
  articleSchema
);

export const ArticleLikeModel = dynamoose.model<ArticleLikeDocument>(
  `${tablePrefix}article_likes`, 
  articleLikeSchema
);