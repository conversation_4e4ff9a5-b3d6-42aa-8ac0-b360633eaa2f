// functions/articles/unlikeArticle.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ArticleService } from './articles-services';
import { LikeArticleRequest, ApiResponse } from '../common/types/articles-types';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const body: LikeArticleRequest = JSON.parse(event.body || '{}');
    const articleService = new ArticleService();
    
    await articleService.unlikeArticle(body);
    
    const response: ApiResponse<null> = {
      success: true,
      message: 'Article unliked successfully',
    };

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  } catch (error) {
    console.error('Unlike article error:', error);
    
    const response: ApiResponse<null> = {
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
    };

    return {
      statusCode: error instanceof Error && 
        (error.message.includes('required') || error.message.includes('not liked')) ? 400 : 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  }
};