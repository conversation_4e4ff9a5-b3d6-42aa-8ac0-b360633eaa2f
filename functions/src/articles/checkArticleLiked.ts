// functions/articles/checkArticleLiked.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ArticleService } from'./articles-services';
import { ApiResponse } from '../common/types/articles-types';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const articleId = event.pathParameters?.id;
    const userId = event.queryStringParameters?.userId;
    
    if (!articleId || !userId) {
      throw new Error('Article ID and User ID are required');
    }

    const articleService = new ArticleService();
    const isLiked = await articleService.isArticleLikedByUser(articleId, userId);
    
    const response: ApiResponse<{isLiked: boolean}> = {
      success: true,
      data: { isLiked },
    };

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  } catch (error) {
    console.error('Check article liked error:', error);
    
    const response: ApiResponse<null> = {
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
    };

    return {
      statusCode: error instanceof Error && error.message.includes('required') ? 400 : 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  }
};