// functions/articles/getUserLikedArticles.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ArticleService } from './articles-services';
import { GetLikedArticlesQuery, ApiResponse } from '../common/types/articles-types';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const userId = event.pathParameters?.userId;
    if (!userId) {
      throw new Error('User ID is required');
    }

    const queryParams = event.queryStringParameters || {};
    
    const query: GetLikedArticlesQuery = {
      userId,
      limit: queryParams.limit ? parseInt(queryParams.limit) : undefined,
      lastKey: queryParams.lastKey,
    };

    const articleService = new ArticleService();
    const articles = await articleService.getUserLikedArticles(query);
    
    const response: ApiResponse<typeof articles> = {
      success: true,
      data: articles,
    };

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  } catch (error) {
    console.error('Get user liked articles error:', error);
    
    const response: ApiResponse<null> = {
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
    };

    return {
      statusCode: error instanceof Error && error.message.includes('required') ? 400 : 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  }
};