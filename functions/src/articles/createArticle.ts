// src/articles/createArticle.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ArticleService } from './articles-services';
import { CreateArticleRequest, ArticlesApiResponse } from '../common/types/articles-types';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const body: CreateArticleRequest = JSON.parse(event.body || '{}');
    const articleService = new ArticleService();
    
    const article = await articleService.createArticle(body);
    
    const response: ArticlesApiResponse<typeof article> = {
      success: true,
      data: article,
    };

    return {
      statusCode: 201,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  } catch (error) {
    console.error('Create article error:', error);
    
    const response: ArticlesApiResponse<null> = {
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
    };

    return {
      statusCode: error instanceof Error && error.message.includes('required') ? 400 : 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  }
};