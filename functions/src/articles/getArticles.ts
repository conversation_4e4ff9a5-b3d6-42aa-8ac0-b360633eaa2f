// functions/articles/getArticles.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ArticleService } from './articles-services';
import { GetArticlesQuery, ApiResponse } from '../common/types/articles-types';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const queryParams = event.queryStringParameters || {};
    const userId = queryParams.userId; // Optional user ID for like status
    
    const query: GetArticlesQuery = {
      limit: queryParams.limit ? parseInt(queryParams.limit) : undefined,
      lastKey: queryParams.lastKey,
      postedBy: queryParams.postedBy,
      tags: queryParams.tags ? queryParams.tags.split(',') : undefined,
      sortBy: queryParams.sortBy as 'createdAt' | 'likes' | undefined,
      order: queryParams.order as 'asc' | 'desc' | undefined,
    };

    const articleService = new ArticleService();
    
    // Get articles with or without like status based on userId presence
    const articles = userId 
      ? await articleService.getArticlesWithLikeStatus(query, userId)
      : await articleService.getArticles(query);
    
    const response: ApiResponse<typeof articles> = {
      success: true,
      data: articles,
    };

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  } catch (error) {
    console.error('Get articles error:', error);
    
    const response: ApiResponse<null> = {
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
    };

    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  }
};