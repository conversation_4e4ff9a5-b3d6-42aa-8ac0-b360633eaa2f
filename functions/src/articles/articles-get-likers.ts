// functions/getArticleLikers.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ArticleService } from './articles-services';
import { ApiResponse } from '../common/types/articles-types';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const articleId = event.pathParameters?.id;
    if (!articleId) {
      throw new Error('Article ID is required');
    }

    const queryParams = event.queryStringParameters || {};
    
    const limit = queryParams.limit ? parseInt(queryParams.limit) : undefined;
    const lastKey = queryParams.lastKey;

    const articleService = new ArticleService();
    const likers = await articleService.getArticleLikers(articleId, limit, lastKey);
    
    const response: ApiResponse<typeof likers> = {
      success: true,
      data: likers,
    };

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  } catch (error) {
    console.error('Get article likers error:', error);
    
    const response: ApiResponse<null> = {
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
    };

    return {
      statusCode: error instanceof Error && error.message.includes('not found') ? 404 : 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  }
};