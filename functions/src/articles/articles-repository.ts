// src/articles/articles-repository.ts
import { ArticleModel, ArticleLikeModel, ArticleDocument, ArticleLikeDocument } from './articles-models';
import { 
  Article, 
  ArticleLike, 
  ArticleWithLikeStatus,
  CreateArticleRequest, 
  UpdateArticleRequest, 
  GetArticlesQuery,
  GetLikedArticlesQuery,
  PaginatedResponse,
  ArticleLikersResponse
} from '../common/types/articles-types';
import { v4 as uuidv4 } from 'uuid';

export class ArticleRepository {
  async createArticle(data: CreateArticleRequest): Promise<Article> {
    const now = new Date().toISOString();
    const article = await ArticleModel.create({
      id: uuidv4(),
      title: data.title,
      description: data.description,
      imageUrl: data.imageUrl,
      contentUrl: data.contentUrl,
      likes: 0,
      createdAt: now,
      modifiedAt: now,
      postedBy: data.postedBy,
      additionalNotes: data.additionalNotes,
      content: data.content,
      tags: data.tags,
    });

    return this.documentToArticle(article);
  }

  async updateArticle(data: UpdateArticleRequest): Promise<Article | null> {
    const article = await ArticleModel.get(data.id);
    if (!article) return null;

    const updateData: Partial<ArticleDocument> = {
      modifiedAt: new Date().toISOString(),
    };

    if (data.title !== undefined) updateData.title = data.title;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.imageUrl !== undefined) updateData.imageUrl = data.imageUrl;
    if (data.contentUrl !== undefined) updateData.contentUrl = data.contentUrl;
    if (data.additionalNotes !== undefined) updateData.additionalNotes = data.additionalNotes;
    if (data.content !== undefined) updateData.content = data.content;
    if (data.tags !== undefined) updateData.tags = data.tags;

    const updatedArticle = await ArticleModel.update({ id: data.id }, updateData);
    return this.documentToArticle(updatedArticle);
  }

  async deleteArticle(id: string): Promise<boolean> {
    try {
      // Also delete all likes for this article
      const queryResponse = await ArticleLikeModel.query('articleId').eq(id).exec();
      const likes = queryResponse.toJSON();
      
      for (const like of likes) {
        await ArticleLikeModel.delete(like.id);
      }

      await ArticleModel.delete(id);
      return true;
    } catch (error) {
      console.error('Error deleting article:', error);
      return false;
    }
  }

  async getArticle(id: string): Promise<Article | null> {
    const article = await ArticleModel.get(id);
    return article ? this.documentToArticle(article) : null;
  }

  async getArticles(query: GetArticlesQuery): Promise<PaginatedResponse<Article>> {
    let dbQuery: any;

    // Apply filters
    if (query.postedBy) {
      dbQuery = ArticleModel.query('postedBy').eq(query.postedBy);
    } else {
      dbQuery = ArticleModel.scan();
    }

    if (query.tags && query.tags.length > 0) {
      for (let i = 0; i < query.tags.length; i++) {
        if (i === 0) {
          dbQuery = dbQuery.filter('tags').contains(query.tags[i]);
        } else {
          dbQuery = dbQuery.or().filter('tags').contains(query.tags[i]);
        }
      }
    }

    // Apply sorting
    if (query.sortBy === 'likes') {
      if (dbQuery.sort) {
        dbQuery = dbQuery.sort(query.order === 'asc' ? 'ascending' : 'descending');
      }
    } else {
      // Default sort by createdAt
      if (dbQuery.sort) {
        dbQuery = dbQuery.sort(query.order === 'asc' ? 'ascending' : 'descending');
      }
    }

    // Apply pagination
    if (query.limit) {
      dbQuery = dbQuery.limit(query.limit);
    }

    if (query.lastKey) {
      dbQuery = dbQuery.startAt(JSON.parse(query.lastKey));
    }

    const result = await dbQuery.exec();
    const items = result.toJSON ? result.toJSON() : result;
    
    return {
      items: Array.isArray(items) ? items.map(this.documentToArticle) : [],
      nextKey: result.lastKey ? JSON.stringify(result.lastKey) : undefined,
    };
  }

  async likeArticle(articleId: string, userId: string): Promise<boolean> {
    try {
      // Check if already liked
      const result = await ArticleLikeModel.query('articleId').eq(articleId)
        .filter('userId').eq(userId).exec();
      
      const existingLikes = result.toJSON();
      
      if (existingLikes.length > 0) {
        return false; // Already liked
      }

      // Create like
      await ArticleLikeModel.create({
        id: uuidv4(),
        articleId,
        userId,
        createdAt: new Date().toISOString(),
      });

      // Increment article likes count
      const article = await ArticleModel.get(articleId);
      if (article) {
        article.likes = (article.likes || 0) + 1;
        await article.save();
      }
      
      return true;
    } catch (error) {
      console.error('Error liking article:', error);
      throw error;
    }
  }

  async unlikeArticle(articleId: string, userId: string): Promise<boolean> {
    try {
      // Find existing like
      const result = await ArticleLikeModel.query('articleId').eq(articleId)
        .filter('userId').eq(userId).exec();
      
      const existingLikes = result.toJSON();
      
      if (existingLikes.length === 0) {
        return false; // Not liked
      }

      // Delete like
      await ArticleLikeModel.delete(existingLikes[0].id);

      // Decrement article likes count
      const article = await ArticleModel.get(articleId);
      if (article && article.likes > 0) {
        article.likes = article.likes - 1;
        await article.save();
      }
      
      return true;
    } catch (error) {
      console.error('Error unliking article:', error);
      throw error;
    }
  }

  async getUserLikedArticles(query: GetLikedArticlesQuery): Promise<PaginatedResponse<Article>> {
    let dbQuery = ArticleLikeModel.query('userId').eq(query.userId);

    if (query.limit) {
      dbQuery = dbQuery.limit(query.limit);
    }

    if (query.lastKey) {
      dbQuery = dbQuery.startAt(JSON.parse(query.lastKey));
    }

    const result = await dbQuery.exec();
    const likes = result.toJSON();
    
    // Get articles for these likes
    const articles: Article[] = [];
    for (const like of likes) {
      const article = await this.getArticle(like.articleId);
      if (article) {
        articles.push(article);
      }
    }

    return {
      items: articles,
      nextKey: result.lastKey ? JSON.stringify(result.lastKey) : undefined,
    };
  }

  async isArticleLikedByUser(articleId: string, userId: string): Promise<boolean> {
    const result = await ArticleLikeModel.query('userId').eq(userId)
      .filter('articleId').eq(articleId).exec();
    
    const likes = result.toJSON();
    return likes.length > 0;
  }

  // Optimized batch check for multiple articles
  async getUserLikedArticleIds(userId: string, articleIds: string[]): Promise<Set<string>> {
    if (articleIds.length === 0) {
      return new Set();
    }

    // We can't use 'in' condition here since it's not supported in all cases
    // Instead, we'll fetch all user likes and filter in memory
    const result = await ArticleLikeModel.query('userId').eq(userId).exec();
    const likes = result.toJSON();
    
    // Filter to only likes for the specified articleIds
    const filteredLikes = likes.filter(like => articleIds.includes(like.articleId));
    
    return new Set(filteredLikes.map(like => like.articleId));
  }
  
  // Get users who liked an article
  async getArticleLikers(articleId: string, limit?: number, lastKey?: string): Promise<ArticleLikersResponse> {
    let dbQuery = ArticleLikeModel.query('articleId').eq(articleId);

    if (limit) {
      dbQuery = dbQuery.limit(limit);
    }

    if (lastKey) {
      dbQuery = dbQuery.startAt(JSON.parse(lastKey));
    }

    const result = await dbQuery.exec();
    const likes = result.toJSON();
    
    // Get article to check total likes count
    const article = await ArticleModel.get(articleId);
    
    return {
      items: likes.map(like => ({
        userId: like.userId,
        createdAt: like.createdAt
      })),
      nextKey: result.lastKey ? JSON.stringify(result.lastKey) : undefined,
      totalLikes: article ? article.likes : 0
    };
  }

  private documentToArticle(doc: ArticleDocument | Record<string, any>): Article {
    return {
      id: doc.id,
      title: doc.title,
      description: doc.description,
      imageUrl: doc.imageUrl,
      contentUrl: doc.contentUrl,
      likes: doc.likes,
      createdAt: doc.createdAt,
      modifiedAt: doc.modifiedAt,
      postedBy: doc.postedBy,
      additionalNotes: doc.additionalNotes,
      content: doc.content,
      tags: doc.tags,
    };
  }
}