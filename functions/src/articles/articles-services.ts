// src/articles/articles-services.ts
import { ArticleRepository } from './articles-repository';
import { 
  Article, 
  ArticleWithLikeStatus,
  CreateArticleRequest, 
  UpdateArticleRequest, 
  LikeArticleRequest,
  GetArticlesQuery,
  GetLikedArticlesQuery,
  PaginatedResponse,
  ArticleLikersResponse
} from '../common/types/articles-types';

export class ArticleService {
  private articleRepository: ArticleRepository;

  constructor() {
    this.articleRepository = new ArticleRepository();
  }

  async createArticle(data: CreateArticleRequest): Promise<Article> {
    // Validate input
    if (!data.title || data.title.trim().length === 0) {
      throw new Error('Title is required');
    }
    if (!data.description || data.description.trim().length === 0) {
      throw new Error('Description is required');
    }
    if (!data.postedBy || data.postedBy.trim().length === 0) {
      throw new Error('Posted by is required');
    }

    return await this.articleRepository.createArticle(data);
  }

  async updateArticle(data: UpdateArticleRequest): Promise<Article> {
    if (!data.id || data.id.trim().length === 0) {
      throw new Error('Article ID is required');
    }

    const article = await this.articleRepository.updateArticle(data);
    if (!article) {
      throw new Error('Article not found');
    }

    return article;
  }

  async deleteArticle(id: string): Promise<void> {
    if (!id || id.trim().length === 0) {
      throw new Error('Article ID is required');
    }

    const success = await this.articleRepository.deleteArticle(id);
    if (!success) {
      throw new Error('Failed to delete article');
    }
  }

  async getArticle(id: string): Promise<Article> {
    if (!id || id.trim().length === 0) {
      throw new Error('Article ID is required');
    }

    const article = await this.articleRepository.getArticle(id);
    if (!article) {
      throw new Error('Article not found');
    }

    return article;
  }

  async getArticles(query: GetArticlesQuery = {}): Promise<PaginatedResponse<Article>> {
    return await this.articleRepository.getArticles(query);
  }

  async getArticlesWithLikeStatus(query: GetArticlesQuery = {}, userId?: string): Promise<PaginatedResponse<ArticleWithLikeStatus>> {
    const articlesResponse = await this.articleRepository.getArticles(query);
    
    if (!userId || articlesResponse.items.length === 0) {
      return {
        ...articlesResponse,
        items: articlesResponse.items.map((article: Article) => ({
          ...article,
          isLikedByUser: false
        }))
      };
    }
    
    // Optimized batch check for liked articles
    const articleIds = articlesResponse.items.map((a: Article) => a.id);
    const likedArticleIds = await this.articleRepository.getUserLikedArticleIds(userId, articleIds);
    
    // Enhance articles with like status
    const itemsWithLikeStatus = articlesResponse.items.map((article: Article) => ({
      ...article,
      isLikedByUser: likedArticleIds.has(article.id)
    }));
    
    return {
      ...articlesResponse,
      items: itemsWithLikeStatus
    };
  }

  async likeArticle(data: LikeArticleRequest): Promise<void> {
    if (!data.articleId || data.articleId.trim().length === 0) {
      throw new Error('Article ID is required');
    }
    if (!data.userId || data.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    // Verify article exists
    const article = await this.articleRepository.getArticle(data.articleId);
    if (!article) {
      throw new Error('Article not found');
    }

    const success = await this.articleRepository.likeArticle(data.articleId, data.userId);
    if (!success) {
      throw new Error('Article already liked by user');
    }
  }

  async unlikeArticle(data: LikeArticleRequest): Promise<void> {
    if (!data.articleId || data.articleId.trim().length === 0) {
      throw new Error('Article ID is required');
    }
    if (!data.userId || data.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    const success = await this.articleRepository.unlikeArticle(data.articleId, data.userId);
    if (!success) {
      throw new Error('Article not liked by user');
    }
  }

  async getUserLikedArticles(query: GetLikedArticlesQuery): Promise<PaginatedResponse<Article>> {
    if (!query.userId || query.userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    return await this.articleRepository.getUserLikedArticles(query);
  }

  async isArticleLikedByUser(articleId: string, userId: string): Promise<boolean> {
    if (!articleId || articleId.trim().length === 0) {
      throw new Error('Article ID is required');
    }
    if (!userId || userId.trim().length === 0) {
      throw new Error('User ID is required');
    }

    return await this.articleRepository.isArticleLikedByUser(articleId, userId);
  }
  
  async getArticleLikers(articleId: string, limit?: number, lastKey?: string): Promise<ArticleLikersResponse> {
    if (!articleId || articleId.trim().length === 0) {
      throw new Error('Article ID is required');
    }

    // Verify article exists
    const article = await this.articleRepository.getArticle(articleId);
    if (!article) {
      throw new Error('Article not found');
    }

    return await this.articleRepository.getArticleLikers(articleId, limit, lastKey);
  }
}