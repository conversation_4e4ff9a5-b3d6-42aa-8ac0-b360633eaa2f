// functions/articles/likeArticle.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ArticleService } from './articles-services';
import { LikeArticleRequest, ApiResponse } from '../common/types/articles-types';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const body: LikeArticleRequest = JSON.parse(event.body || '{}');
    const articleService = new ArticleService();
    
    await articleService.likeArticle(body);
    
    const response: ApiResponse<null> = {
      success: true,
      message: 'Article liked successfully',
    };

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  } catch (error) {
    console.error('Like article error:', error);
    
    const response: ApiResponse<null> = {
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
    };

    return {
      statusCode: error instanceof Error && 
        (error.message.includes('required') || error.message.includes('already liked')) ? 400 : 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  }
};