// functions/articles/updateArticle.ts
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ArticleService } from './articles-services';
import { UpdateArticleRequest, ApiResponse } from '../common/types/articles-types';

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    const id = event.pathParameters?.id;
    if (!id) {
      throw new Error('Article ID is required');
    }

    const body: Omit<UpdateArticleRequest, 'id'> = JSON.parse(event.body || '{}');
    const articleService = new ArticleService();
    
    const article = await articleService.updateArticle({ ...body, id });
    
    const response: ApiResponse<typeof article> = {
      success: true,
      data: article,
    };

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  } catch (error) {
    console.error('Update article error:', error);
    
    const response: ApiResponse<null> = {
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
    };

    return {
      statusCode: error instanceof Error && 
        (error.message.includes('required') || error.message.includes('not found')) ? 400 : 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify(response),
    };
  }
};