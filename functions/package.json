{"name": "thyview-lambda-functions", "scripts": {"lint": "eslint --ext .ts ./src", "lint:fix": "eslint --ext .ts ./src --fix", "build": "tsc", "clean": "rimraf node_modules/ dist/ lib/ .webpack/ coverage/ package-lock.json .build .serverless", "deploy": "cd .. && serverless deploy", "deploy:dev": "cd .. && serverless deploy --stage dev", "deploy:stage": "cd .. && serverless deploy --stage stage", "deploy:prod": "cd .. && serverless deploy --stage prod", "deploy:function": "cd .. && serverless deploy function", "deploy:function:dev": "cd .. && serverless deploy function --stage dev", "deploy:function:stage": "cd .. && serverless deploy function --stage stage", "deploy:function:prod": "cd .. && serverless deploy function --stage prod", "deploy:single": "cd .. && ./scripts/deploy-single-function.sh", "deploy:group": "cd .. && ./scripts/deploy-api-group.sh", "start": "cd .. && serverless offline", "start:local-server": "bash ./start-local-server.sh", "logs": "cd .. && serverless logs", "logs:tail": "cd .. && serverless logs --tail", "invoke": "cd .. && serverless invoke", "test:function": "cd .. && serverless invoke", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:movie-search": "jest --testPathPattern=movie.*search", "test:integration": "jest --testPathPattern=integration"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"@aws-sdk/client-dynamodb": "^3.521.0", "@aws-sdk/client-s3": "^3.848.0", "@aws-sdk/client-sns": "^3.521.0", "@aws-sdk/client-sqs": "^3.521.0", "@aws-sdk/lib-dynamodb": "^3.521.0", "@aws-sdk/s3-request-presigner": "^3.848.0", "aws-cdk-lib": "^2.195.0", "aws-lambda": "^1.0.7", "canvas": "^3.1.0", "constructs": "^10.4.2", "crypto-ts": "^1.0.2", "dynamoose": "^4.0.1", "flake-idgen": "^1.4.0", "googleapis": "^134.0.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "lambda-log": "^3.1.0", "moment": "^2.30.1", "node-fetch": "^3.3.2", "uuid": "^9.0.1"}, "devDependencies": {"@types/aws-lambda": "^8.10.134", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^22.2.0", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "esbuild": "^0.20.1", "eslint": "^8.57.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "rimraf": "^5.0.5", "serverless-dotenv-plugin": "^6.0.0", "serverless-dynamodb-local": "^0.2.40", "serverless-esbuild": "^1.52.0", "serverless-offline": "^13.4.0", "serverless-plugin-typescript": "^2.1.5", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.4.2"}, "private": true}