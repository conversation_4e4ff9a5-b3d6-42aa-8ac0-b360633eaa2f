#!/bin/bash

# <PERSON>ript to clean up build directories, kill any running processes on the required ports, 
# and start serverless offline with a patched build process

echo "=============================================="
echo "Starting serverless with patched build process"
echo "=============================================="

# Store the current directory
CURRENT_DIR=$(pwd)

# Function to kill process using a specific port
kill_process_on_port() {
  local PORT=$1
  echo "Checking for processes on port $PORT..."
  
  # For Mac
  if [[ "$OSTYPE" == "darwin"* ]]; then
    local PID=$(lsof -i :$PORT -t)
    if [ ! -z "$PID" ]; then
      echo "Killing process $PID using port $PORT"
      kill -9 $PID
    else
      echo "No process found using port $PORT"
    fi
  # For Linux
  elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    local PID=$(netstat -tulpn 2>/dev/null | grep ":$PORT " | awk '{print $7}' | cut -d'/' -f1)
    if [ ! -z "$PID" ]; then
      echo "Killing process $PID using port $PORT"
      kill -9 $PID
    else
      echo "No process found using port $PORT"
    fi
  else
    echo "Unsupported OS for port killing. Please manually ensure ports 3000, 3002, and 3003 are free."
  fi
}

# Kill processes on ports used by serverless-offline
echo "Killing any processes using serverless-offline ports..."
kill_process_on_port 3000  # HTTP port
kill_process_on_port 3002  # Default Lambda port
kill_process_on_port 3003  # Our custom Lambda port
# Give the OS a moment to release the ports
sleep 1

# Navigate to the parent directory where serverless.yml is located
cd ..

# Clean up any existing build files
echo "Cleaning up build directories..."
rm -rf .build
rm -rf .serverless

# Create a simple patch script that will prevent the symlink creation
echo "Creating build patch..."
cat > build-patch.js << 'EOF'
const fs = require('fs');

// Function to check if a module is installed
function isModuleInstalled(moduleName) {
  try {
    require.resolve(moduleName);
    return true;
  } catch (e) {
    return false;
  }
}

// Apply patch to serverless-esbuild if it exists
if (isModuleInstalled('serverless-esbuild')) {
  try {
    const esbuildHelperPath = require.resolve('serverless-esbuild/dist/helper');
    const content = fs.readFileSync(esbuildHelperPath, 'utf8');
    
    // Skip symlink creation by replacing the function
    const patchedContent = content.replace(
      /function symlinkNodeModules\(([^)]+)\)\s*{[\s\S]+?return\s+[^;]+;}/,
      'function symlinkNodeModules($1) { console.log("Skipping node_modules symlink"); return Promise.resolve(); }'
    );
    
    if (content !== patchedContent) {
      fs.writeFileSync(esbuildHelperPath, patchedContent);
      console.log('✅ Successfully patched serverless-esbuild to avoid symlink error');
    } else {
      console.log('ℹ️ Patch already applied or pattern not found');
    }
  } catch (err) {
    console.error('❌ Failed to patch serverless-esbuild:', err);
  }
} else {
  console.log('⚠️ serverless-esbuild not found, skipping patch');
}
EOF

# Run the patch script
echo "Applying patch..."
node build-patch.js

# Start serverless offline
echo "Starting serverless offline..."
NODE_OPTIONS="--no-warnings" serverless offline

# Return to the original directory
cd $CURRENT_DIR