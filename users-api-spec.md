## Thyview User CRUD API Specification

### Base URL:

```
https://{api-id}.execute-api.{region}.amazonaws.com/{stage}
```

### Authentication:

* All endpoints require Firebase Authorization token in the header:

```
Authorization: Bearer <Firebase ID Token>
```

---

### 1. Create User

**POST** `/users`

**Request Body (JSON):**

```json
{
  "id": "firebase-auth-uid",
  "username": "john_doe",
  "email": "<EMAIL>",
  "name": "<PERSON>"
}
```

**Response:** `200 OK`

```json
{
  "id": "firebase-auth-uid",
  "username": "john_doe",
  "email": "<EMAIL>",
  "name": "<PERSON>",
  "role": "user",
  "createdAt": "...",
  "updatedAt": "...",
  "externalUserId": "913035179246443"
}
```

---

### 2. Get User by ID or ExternalUserId

**GET** `/users?id=<id>`

or

**GET** `/users?externalUserId=<externalUserId>`

**Response:** `200 OK`

```json
{
  "id": "firebase-auth-uid",
  "username": "john_doe",
  "email": "<EMAIL>",
  "role": "user",
  ...
}
```

---

### 3. Get User by Username

**GET** `/users/username?username=john_doe`

**Response:** `200 OK`

```json
{
  "id": "firebase-auth-uid",
  "username": "john_doe",
  "email": "<EMAIL>",
  "role": "user",
  ...
}
```

---

### 4. Update User

**PUT** `/users/{id}`

**Request Body (JSON):**

```json
{
  "name": "John Updated",
  "bio": "New bio"
}
```

**Response:** `200 OK`

```json
{
  "id": "firebase-auth-uid",
  "name": "John Updated",
  "bio": "New bio",
  "role": "user",
  ...
}
```

---

### 5. Delete User

**DELETE** `/users/{id}`

**Response:** `200 OK`

```json
{
  "message": "User deleted successfully."
}
```

---

## Postman Collection Overview:

1. Create User - POST `/users`
2. Get User by ID - GET `/users?id=...`
3. Get User by externalUserId - GET `/users?externalUserId=...`
4. Get User by Username - GET `/users/username?username=...`
5. Update User - PUT `/users/{id}`
6. Delete User - DELETE `/users/{id}`

Each request should include:

```
Authorization: Bearer <Your Firebase Token>
Content-Type: application/json
```
