# ThyView AWS Lambda Deployment Guide

## Key Features

- **Universal Single Function Deployment**: Deploy ANY function individually with automatic infrastructure
- **Intelligent Resource Creation**: Only creates tables/resources needed for the specific function
- **Environment Support**: dev, stage, prod environments
- **Resource Naming**: Consistent `resourceName-env` pattern (e.g., `movieSearch-stage`)
- **No Limitations**: Works with all 30+ functions in the project

## Prerequisites
- Node.js 20.x installed
- AWS CLI configured (`aws configure`)
- Serverless Framework installed (`npm install -g serverless`)

## Quick Setup
```bash
# Configure AWS credentials
aws configure

# Install dependencies
npm install
cd functions && npm install && cd ..
```

## Deployment Commands

### 1. Deploy Full Stack
```bash
npm run deploy:dev      # Deploy all functions to development
npm run deploy:stage    # Deploy all functions to staging  
npm run deploy:prod     # Deploy all functions to production

# Or use the script directly
./scripts/deploy.sh stage               # Deploy full stack to stage
./scripts/deploy.sh prod                # Deploy full stack to prod
```

### 2. Deploy Single Function
```bash
# The script automatically handles infrastructure creation if needed
# Works with ANY function from serverless.yml
./scripts/deploy.sh stage movieSearch   # Deploy only movieSearch to stage
./scripts/deploy.sh prod createPost     # Deploy only createPost to prod
./scripts/deploy.sh dev getArticles     # Deploy only getArticles to dev
./scripts/deploy.sh stage likeArticle   # Deploy only likeArticle to stage
./scripts/deploy.sh prod addComment     # Deploy only addComment to prod

# The script intelligently creates only the resources needed for that function:
# - Movie functions: Creates search logs table
# - Post functions: Creates posts + postlikes tables
# - Article functions: Creates articles + articlelikes tables
# - Comment functions: Creates comments + commentlikes tables
# - Upload functions: Creates S3 bucket + required tables
```


# Local development
# Start local server (all functions available)
cd functions && npm run start:local-server
# Server: http://localhost:3000
# Endpoints: http://localhost:3000/v1/dev/{endpoint}

## Resource Naming Convention

All resources follow the pattern: `resourceName-stage`

### Functions
- `movieSearch-stage`
- `createPost-stage`
- `firebaseAuthorizer-stage`

### DynamoDB Tables
- `posts-stage`
- `postlikes-stage`
- `comments-stage`
- `commentlikes-stage`
- `articles-stage`
- `articlelikes-stage`
- `users-stage`
- `searchlogs-stage`
- `postreports-stage`
- `commentreports-stage`

### API Gateway
- `thyview-api-stage`

### S3 Bucket
- `thyview-storage-stage`

## Movie Search API

### Endpoint
```
GET /movie/search?q={query}
Authorization: Bearer <firebase-token>
```

### Response Format
```json
{
  "query": "batman",
  "type": "movie",
  "results": [...],
  "meta": {
    "requestId": "abc123",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

## Available Functions

### Movie APIs
- `movieSearch` - Search movies, TV shows, people
- `movieDetails` - Get movie details
- `tvDetails` - Get TV show details
- `personDetails` - Get person details
- `movieTrending` - Get trending movies

### Post APIs
- `createPost` - Create new post
- `getPostsList` - List posts
- `editPost` - Edit post
- `deletePost` - Delete post
- `likePost` - Like/unlike post
- `getPostDetail` - Get post details

### Article APIs
- `createArticle` - Create article
- `getArticles` - List articles
- `getArticle` - Get article
- `updateArticle` - Update article
- `deleteArticle` - Delete article
- `likeArticle` - Like article
- `unlikeArticle` - Unlike article

### Other APIs
- `firebaseAuthorizer` - Authentication handler
- `addComment` - Add comment
- `getComments` - Get comments
- `reportPost` - Report post

## Useful Commands

### View Logs
```bash
serverless logs --function movieSearch --stage stage --tail
```

### Test Function
```bash
serverless invoke --function movieSearch --stage stage
```

### Get Deployment Info
```bash
serverless info --stage stage
```

### Remove Deployment
```bash
serverless remove --stage stage
```

## Troubleshooting

### Common Issues

**"Function not yet deployed" Error:**
- Solution: The script automatically handles this by deploying infrastructure first

**"Stack does not exist" Error:**
- Solution: Run `./scripts/deploy.sh stage functionName` - it will create the stack

**AWS Credentials Error:**
- Solution: Run `aws configure` and enter your credentials

### Examples

```bash
# Deploy movie search to staging
./scripts/deploy.sh stage movieSearch

# Deploy full stack to production
npm run deploy:prod

# Check if deployment exists
npm run check:stack:stage

# View movie search logs
serverless logs --function movieSearch --stage stage --tail
```

This simplified guide covers everything you need to deploy ThyView functions with the new naming convention.
