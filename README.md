# ThyView Cloud Functions AWS

This project contains serverless cloud functions for the ThyView application, deployed on AWS Lambda.

## Key Features

- **Universal Single Function Deployment**: Deploy ANY function individually with automatic infrastructure
- **Intelligent Resource Creation**: Only creates tables/resources needed for the specific function
- **Environment Support**: dev, stage, prod environments
- **Resource Naming**: Consistent `resourceName-env` pattern (e.g., `movieSearch-stage`)
- **Local Development**: Full local server with hot reload

## Project Structure

```
├── config/                  # Configuration files
│   └── environments/        # Environment-specific configuration
├── functions/               # Lambda functions source code
│   ├── src/                 # Source code
│   │   ├── articles/        # Article-related functions
│   │   ├── authorizer/      # Authentication and authorization
│   │   ├── common/          # Shared code
│   │   │   ├── config/      # Configuration
│   │   │   ├── middleware/  # Middleware functions
│   │   │   ├── models/      # Data models
│   │   │   ├── types/       # TypeScript type definitions
│   │   │   └── utils/       # Utility functions
│   │   ├── movie/           # Movie-related functions
│   │   ├── reviews/         # Review-related functions
│   │   └── services/        # External service integrations
│   ├── tests/               # Test files
│   │   ├── integration/     # Integration tests
│   │   └── unit/            # Unit tests
│   ├── .github/             # GitHub related files
│   │   └── workflows/       # GitHub Actions workflows
│   ├── dist/                # Compiled JavaScript output
│   └── webpack.config.js    # Webpack configuration
├── infrastructure/          # Infrastructure as code definitions
├── scripts/                 # Utility scripts
├── serverless.yml           # Serverless Framework configuration
├── package.json             # Project dependencies and scripts
└── README.md                # Project documentation
```

## Getting Started

### Prerequisites

- Node.js 22.x
- AWS CLI configured with appropriate credentials
- Serverless Framework installed globally


# Running Locally. Just this command is enough, it will clean, install, build and start local server
```bash
redis-server
npm install && npm run start:local-server
```
# Kill redis ports
ps aux | grep redis
kill -9 <pid>
#

# Install Redis if not installed
brew install redis  # macOS

# Start Redis
redis-server

### Installation

```bash
# Install dependencies
cd functions
npm install

```
# build
```bash
npm run build
```    

### Local Development

```bash
# Start local development server
npm run start
```

### Deployment

#### Deploy Full Stack
```bash
npm run deploy:dev      # Deploy all functions to development
npm run deploy:stage    # Deploy all functions to staging
npm run deploy:prod     # Deploy all functions to production
```

#### Deploy Single Function (ANY Function!)
```bash
# Works with ANY function from serverless.yml
./scripts/deploy.sh stage movieSearch   # Deploy only movieSearch to stage
./scripts/deploy.sh prod createPost     # Deploy only createPost to prod
./scripts/deploy.sh dev getArticles     # Deploy only getArticles to dev
./scripts/deploy.sh stage likeArticle   # Deploy only likeArticle to stage
./scripts/deploy.sh prod addComment     # Deploy only addComment to prod

# The script intelligently creates only the resources needed for that function:
# - Movie functions: Creates search logs table
# - Post functions: Creates posts + postlikes tables
# - Article functions: Creates articles + articlelikes tables
# - Comment functions: Creates comments + commentlikes tables
# - Upload functions: Creates S3 bucket + required tables
```

#### Local Development
```bash
cd functions && npm run start:local-server
# Server runs on http://localhost:3000
# API endpoints: http://localhost:3000/v1/dev/{endpoint}
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage report
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## Movie Search API Documentation

### Overview
The movie search API provides intelligent search functionality for movies, TV shows, and people using The Movie Database (TMDB) API with machine learning-powered query classification.

### Endpoint
```
GET /movie/search?q={query}
```

### Authentication
Requires Firebase authentication token in the Authorization header:
```
Authorization: Bearer <firebase-token>
```

### Query Parameters
- `q` (required): Search query string (minimum 3 characters)

### Request Example
```bash
curl -X GET "https://your-api-gateway-url/movie/search?q=batman" \
  -H "Authorization: Bearer your-firebase-token" \
  -H "Content-Type: application/json"
```

### Response Format
```json
{
  "query": "batman",
  "type": "movie",
  "results": [
    {
      "id": 272,
      "type": "movie",
      "title": "Batman Begins",
      "release_year": 2005,
      "poster": "https://image.tmdb.org/t/p/w500/poster.jpg",
      "overview": "Driven by tragedy, billionaire Bruce Wayne...",
      "runtime": 140,
      "rating": 8.2,
      "genres": ["Action", "Crime", "Drama"],
      "director": "Christopher Nolan",
      "cast": ["Christian Bale", "Michael Caine"]
    }
  ],
  "meta": {
    "requestId": "abc123",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

### Error Responses
```json
{
  "error": "Bad Request",
  "message": "Query parameter q is required",
  "requestId": "abc123"
}
```

### Query Classification
The API automatically classifies queries into:
- `movie`: Movie titles and related terms
- `tv`: TV show titles and related terms
- `person`: Actor, director, or crew member names
- `genre`: Genre-based searches (action, comedy, etc.)
- `multi`: General searches

### Deployment Commands for Movie Search API

```bash
# Deploy movie search function only
./scripts/deploy.sh stage movieSearch
./scripts/deploy.sh prod movieSearch

# Deploy full stack
npm run deploy:stage
npm run deploy:prod

# View logs
serverless logs --function movieSearch --stage stage --tail

# Test function
serverless invoke --function movieSearch --stage stage
```

**Function Name in AWS**: `movieSearch-stage`

## Environment Variables

Environment-specific variables are stored in `config/environments/*.env` files. These are loaded based on the deployment stage.

### Required Environment Variables
- `TMDB_API_KEY`: The Movie Database API key
- `HUGGING_FACE_TOKEN`: Hugging Face API token for query classification
- `REDIS_HOST`: Redis server hostname
- `REDIS_PORT`: Redis server port
- `REDIS_PASSWORD`: Redis server password

## CI/CD

This project uses GitHub Actions for CI/CD. The pipelines are configured in `.github/workflows/`.

## AWS Lambda Deployment Prerequisites

### AWS Account Setup

1. **Create AWS Account**: Sign up at [AWS Console](https://aws.amazon.com/console/)

2. **Create IAM User for Deployment**:
   ```bash
   # Create IAM user with programmatic access
   aws iam create-user --user-name thyview-deployer

   # Attach necessary policies
   aws iam attach-user-policy --user-name thyview-deployer --policy-arn arn:aws:iam::aws:policy/AWSLambdaFullAccess
   aws iam attach-user-policy --user-name thyview-deployer --policy-arn arn:aws:iam::aws:policy/IAMFullAccess
   aws iam attach-user-policy --user-name thyview-deployer --policy-arn arn:aws:iam::aws:policy/CloudWatchLogsFullAccess
   aws iam attach-user-policy --user-name thyview-deployer --policy-arn arn:aws:iam::aws:policy/AmazonS3FullAccess
   aws iam attach-user-policy --user-name thyview-deployer --policy-arn arn:aws:iam::aws:policy/AmazonAPIGatewayAdministrator
   aws iam attach-user-policy --user-name thyview-deployer --policy-arn arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess

   # Create access keys
   aws iam create-access-key --user-name thyview-deployer
   ```

3. **Configure AWS Regions**:
   - Primary: `us-east-1` (N. Virginia) - Default for this project
   - Alternative: `us-west-2` (Oregon), `eu-west-1` (Ireland)

### Required AWS Services Setup

#### 1. DynamoDB Tables
The application will automatically create these tables during deployment:
- `ThyView-{stage}-posts`
- `ThyView-{stage}-post_likes`
- `ThyView-{stage}-comments`
- `ThyView-{stage}-comment_likes`
- `ThyView-{stage}-users`
- `ThyView-{stage}-articles`
- `ThyView-{stage}-article_likes`
- `search-logs-{stage}`

#### 2. S3 Buckets
- `thyview-storage-{stage}` - File storage
- `thyview-serverless-deployments-{stage}` - Deployment artifacts

#### 3. ElastiCache Redis (Optional for Production)
```bash
# Create Redis cluster for production
aws elasticache create-cache-cluster \
  --cache-cluster-id thyview-redis-prod \
  --engine redis \
  --cache-node-type cache.t3.micro \
  --num-cache-nodes 1 \
  --port 6379
```

## Comprehensive Deployment Guide

### Prerequisites Installation

#### 1. Node.js Installation

This project requires Node.js 20.x.

**For macOS:**
```bash
# Using Homebrew
brew install node@20

# Verify installation
node --version
npm --version
```

**For Windows:**
- Download and install from [Node.js official website](https://nodejs.org/)
- Select the LTS version that is version 20.x

**For Linux:**
```bash
# Using nvm (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash
source ~/.bashrc
nvm install 20
nvm use 20

# Verify installation
node --version
npm --version
```

#### 2. AWS CLI Installation and Configuration

**Installation:**

**For macOS:**
```bash
# Using Homebrew
brew install awscli

# Verify installation
aws --version
```

**For Windows:**
- Download and run the [AWS CLI MSI installer](https://awscli.amazonaws.com/AWSCLIV2.msi)
- Follow the installation wizard

**For Linux:**
```bash
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Verify installation
aws --version
```

**AWS CLI Configuration:**
```bash
aws configure
```

You will be prompted to enter:
- AWS Access Key ID
- AWS Secret Access Key
- Default region name (e.g., us-east-1)
- Default output format (json is recommended)

To obtain AWS access keys:
1. Log in to the [AWS Management Console](https://aws.amazon.com/console/)
2. Navigate to IAM (Identity and Access Management)
3. Select "Users" and then your username
4. Go to the "Security credentials" tab
5. Click "Create access key"
6. Download or note the key pair (you will not be able to see the secret key again)

Ensure your IAM user has the following permissions:
- AWSLambdaFullAccess
- IAMFullAccess (or more restricted policy that allows creating Lambda execution roles)
- CloudWatchLogsFullAccess
- AmazonS3FullAccess
- AmazonAPIGatewayAdministrator

#### 3. Serverless Framework Installation

```bash
# Install serverless globally
npm install -g serverless

# Verify installation
serverless --version
# or
sls --version
```

### Deploying a Single Lambda Function

#### 1. Clone and Set Up the Project

```bash
# Clone the repository (if you haven't already)
git clone <repository-url>
cd thyview-cloud-functions-aws

# Install project dependencies
cd functions
npm install
```

#### 2. Configure Environment Variables

Create or update environment variables in the appropriate file:

```bash
# For development environment
cp config/environments/dev.env.example config/environments/dev.env
# Edit the file with your environment-specific values

# For production environment
cp config/environments/prod.env.example config/environments/prod.env
# Edit the file with your environment-specific values
```

#### 3. Deploy a Single Function

Using the Serverless Framework, you can deploy a single function instead of the entire service:

```bash
# Format:
# serverless deploy function --function <function-name> --stage <stage-name>

# Example to deploy a function to development stage
cd functions
serverless deploy function --function reviewPostCreate --stage dev

# For production
serverless deploy function --function reviewPostCreate --stage prod
```

You can also use the npm scripts from the package.json to deploy a single function:

```bash
# For development
npm run deploy:function -- --function reviewPostCreate --stage dev

# For production
npm run deploy:function -- --function reviewPostCreate --stage prod
```

#### 4. Testing the Deployed Function

After deployment, you can test your Lambda function:

```bash
# Invoke the function remotely
serverless invoke --function reviewPostCreate --stage dev --data '{"body": "{\"key\": \"value\"}"}'

# View logs for the function
serverless logs --function reviewPostCreate --stage dev --tail
```

### Testing Deployed Functions

#### Local Testing
```bash
# Test movie search function locally
curl "http://localhost:3000/movie/search?q=batman" \
  -H "Authorization: Bearer test-token"

# Test with different query types
curl "http://localhost:3000/movie/search?q=action"  # Genre search
curl "http://localhost:3000/movie/search?q=tom+hanks"  # Person search
curl "http://localhost:3000/movie/search?q=breaking+bad"  # TV search
```

#### Remote Testing
```bash
# Test deployed function
serverless invoke --function movieSearch --stage dev --data '{
  "queryStringParameters": {"q": "batman"},
  "headers": {"Authorization": "Bearer test-token"},
  "requestContext": {"authorizer": {"userId": "test-user"}}
}'

# Test with curl
curl "https://your-api-id.execute-api.us-east-1.amazonaws.com/dev/movie/search?q=batman" \
  -H "Authorization: Bearer your-firebase-token"
```

#### Load Testing
```bash
# Install artillery for load testing
npm install -g artillery

# Create load test configuration
cat > load-test.yml << EOF
config:
  target: 'https://your-api-id.execute-api.us-east-1.amazonaws.com/dev'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Movie Search"
    requests:
      - get:
          url: "/movie/search?q=batman"
          headers:
            Authorization: "Bearer your-firebase-token"
EOF

# Run load test
artillery run load-test.yml
```

### Troubleshooting Common Deployment Issues

#### 1. AWS Credentials Issues
```bash
# Check current AWS configuration
aws sts get-caller-identity

# Reconfigure if needed
aws configure

# Check permissions
aws iam get-user
aws iam list-attached-user-policies --user-name your-username
```

#### 2. Serverless Framework Issues
```bash
# Clear serverless cache
serverless --help  # This clears cache

# Reinstall serverless
npm uninstall -g serverless
npm install -g serverless@latest

# Check serverless configuration
serverless config credentials --provider aws --key YOUR_KEY --secret YOUR_SECRET
```

#### 3. Function-Specific Issues
```bash
# Check function logs
serverless logs --function movieSearch --stage dev --tail

# Invoke function with debug
serverless invoke --function movieSearch --stage dev --log

# Check function configuration
serverless info --stage dev
```

#### 4. Environment Variable Issues
```bash
# Check environment variables in deployed function
serverless invoke --function movieSearch --stage dev --data '{
  "test": "env"
}' --log

# Update environment variables without full deployment
serverless deploy function --function movieSearch --stage dev
```

#### 5. API Gateway Issues
```bash
# Check API Gateway configuration
aws apigateway get-rest-apis

# Test API Gateway directly
aws apigateway test-invoke-method \
  --rest-api-id your-api-id \
  --resource-id your-resource-id \
  --http-method GET \
  --path-with-query-string "/movie/search?q=test"
```

#### 6. Common Error Solutions

**Error: "Function not found"**
```bash
# Ensure function name matches serverless.yml
grep -n "movieSearch" serverless.yml
```

**Error: "Timeout"**
```bash
# Increase timeout in serverless.yml or use parameter
serverless deploy function --function movieSearch --stage dev --aws-client-timeout 60000
```

**Error: "Memory limit exceeded"**
```bash
# Increase memory in serverless.yml
# memorySize: 1024  # or higher
```

**Error: "Cold start issues"**
```bash
# Use provisioned concurrency for production
# Add to function configuration:
# provisionedConcurrency: 2
```

### Rollback Procedures

#### Quick Rollback
```bash
# Rollback to previous deployment
serverless rollback --stage prod

# Rollback specific function
serverless rollback function --function movieSearch --stage prod

# Rollback to specific timestamp
serverless rollback --timestamp 2024-01-01T00:00:00.000Z --stage prod
```

#### Manual Rollback Steps
1. **Identify the issue**: Check CloudWatch logs and metrics
2. **Stop traffic**: Update API Gateway to return maintenance message
3. **Rollback function**: Use serverless rollback command
4. **Verify functionality**: Test the rolled-back function
5. **Restore traffic**: Remove maintenance message
6. **Monitor**: Watch logs and metrics for stability

#### Emergency Procedures
```bash
# Remove problematic function immediately
serverless remove --stage prod

# Deploy known good version
git checkout last-known-good-commit
serverless deploy --stage prod

# Or deploy from backup
serverless deploy --package path/to/backup/package --stage prod
```

### Monitoring and Alerting

#### CloudWatch Dashboards
```bash
# Create custom dashboard for movie search API
aws cloudwatch put-dashboard --dashboard-name "ThyView-MovieSearch" --dashboard-body '{
  "widgets": [
    {
      "type": "metric",
      "properties": {
        "metrics": [
          ["AWS/Lambda", "Duration", "FunctionName", "thyview-api-dev-movieSearch"],
          ["AWS/Lambda", "Errors", "FunctionName", "thyview-api-dev-movieSearch"],
          ["AWS/Lambda", "Invocations", "FunctionName", "thyview-api-dev-movieSearch"]
        ],
        "period": 300,
        "stat": "Average",
        "region": "us-east-1",
        "title": "Movie Search Function Metrics"
      }
    }
  ]
}'
```

#### CloudWatch Alarms
```bash
# Create error rate alarm
aws cloudwatch put-metric-alarm \
  --alarm-name "MovieSearch-HighErrorRate" \
  --alarm-description "Movie search function error rate is high" \
  --metric-name Errors \
  --namespace AWS/Lambda \
  --statistic Sum \
  --period 300 \
  --threshold 5 \
  --comparison-operator GreaterThanThreshold \
  --dimensions Name=FunctionName,Value=thyview-api-prod-movieSearch \
  --evaluation-periods 2

# Create duration alarm
aws cloudwatch put-metric-alarm \
  --alarm-name "MovieSearch-HighDuration" \
  --alarm-description "Movie search function duration is high" \
  --metric-name Duration \
  --namespace AWS/Lambda \
  --statistic Average \
  --period 300 \
  --threshold 10000 \
  --comparison-operator GreaterThanThreshold \
  --dimensions Name=FunctionName,Value=thyview-api-prod-movieSearch \
  --evaluation-periods 2
```

#### Log Analysis
```bash
# Search for errors in CloudWatch logs
aws logs filter-log-events \
  --log-group-name "/aws/lambda/thyview-api-prod-movieSearch" \
  --filter-pattern "ERROR" \
  --start-time $(date -d "1 hour ago" +%s)000

# Get function metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/Lambda \
  --metric-name Invocations \
  --dimensions Name=FunctionName,Value=thyview-api-prod-movieSearch \
  --start-time $(date -d "1 hour ago" --iso-8601) \
  --end-time $(date --iso-8601) \
  --period 300 \
  --statistics Sum
```

### Advanced Configuration

#### Custom Domain Names
```yaml
# Add to serverless.yml
custom:
  customDomain:
    domainName: api.thyview.com
    basePath: 'v1'
    stage: ${self:provider.stage}
    createRoute53Record: true
    certificateName: '*.thyview.com'

plugins:
  - serverless-domain-manager
```

#### VPC Configuration for Production
```yaml
provider:
  vpc:
    securityGroupIds:
      - sg-12345678  # Allow HTTPS outbound for TMDB API
      - sg-87654321  # Allow Redis access
    subnetIds:
      - subnet-12345678  # Private subnet 1
      - subnet-87654321  # Private subnet 2
```

#### Performance Optimization
```yaml
functions:
  movieSearch:
    provisionedConcurrency: 2  # Keep 2 instances warm
    reservedConcurrency: 50    # Limit max concurrent executions
    memorySize: 1024          # Optimize for ML classification
    timeout: 30               # Reasonable timeout
    environment:
      NODE_OPTIONS: '--max-old-space-size=1024'
```

## License

[Specify license information here]
