# Articles API Specification

This document provides detailed specifications for all API endpoints in the Articles AWS backend.

## Authentication

All endpoints require authentication unless explicitly marked as public.

**Authentication Header:**
```
Authorization: Bearer {jwt_token}
```

## Articles

### Create Article
- **Endpoint:** `POST /articles`
- **Handler:** `functions/createArticle.handler`
- **Description:** Creates a new article with optional image, content, and tags
- **Request:**
  ```json
  {
    "title": "Article Title",
    "description": "Article description text",
    "imageUrl": "https://example.com/image.jpg", // Optional
    "contentUrl": "https://example.com/content.html", // Optional
    "content": "Article content text or HTML", // Optional
    "postedBy": "author-id",
    "additionalNotes": "Additional notes about the article", // Optional
    "tags": ["technology", "programming"] // Optional
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "id": "article-uuid",
      "title": "Article Title",
      "description": "Article description text",
      "imageUrl": "https://example.com/image.jpg",
      "contentUrl": "https://example.com/content.html",
      "likes": 0,
      "createdAt": "2025-05-15T12:00:00Z",
      "modifiedAt": "2025-05-15T12:00:00Z",
      "postedBy": "author-id",
      "additionalNotes": "Additional notes about the article",
      "content": "Article content text or HTML",
      "tags": ["technology", "programming"]
    }
  }
  ```

### Get Articles List
- **Endpoint:** `GET /articles`
- **Handler:** `functions/getArticles.handler`
- **Description:** Retrieves a paginated list of articles
- **Query Parameters:**
    - `limit` (optional): Number of articles to retrieve (default: 20)
    - `lastKey` (optional): Key for pagination
    - `postedBy` (optional): Filter by author ID
    - `tags` (optional): Comma-separated tags to filter
    - `sortBy` (optional): Field to sort by ('createdAt' or 'likes')
    - `order` (optional): Sort order ('asc' or 'desc')
    - `userId` (optional): Include like status for this user
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "items": [
        {
          "id": "article-id",
          "title": "Article Title",
          "description": "Article description text",
          "imageUrl": "https://example.com/image.jpg",
          "contentUrl": "https://example.com/content.html",
          "likes": 42,
          "createdAt": "2025-05-15T12:00:00Z",
          "modifiedAt": "2025-05-15T12:00:00Z",
          "postedBy": "author-id",
          "additionalNotes": "Additional notes about the article",
          "content": "Article content text or HTML",
          "tags": ["technology", "programming"],
          "isLikedByUser": true // Only when userId is provided
        }
      ],
      "nextKey": "key-for-next-page"
    }
  }
  ```

### Update Article
- **Endpoint:** `PUT /articles/{id}`
- **Handler:** `functions/updateArticle.handler`
- **Description:** Updates an existing article
- **Request:**
  ```json
  {
    "title": "Updated Title", // Optional
    "description": "Updated description text", // Optional
    "imageUrl": "https://example.com/new-image.jpg", // Optional
    "contentUrl": "https://example.com/new-content.html", // Optional
    "content": "Updated content text or HTML", // Optional
    "additionalNotes": "Updated additional notes", // Optional
    "tags": ["technology", "programming", "typescript"] // Optional
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "id": "article-id",
      "title": "Updated Title",
      "description": "Updated description text",
      "imageUrl": "https://example.com/new-image.jpg",
      "contentUrl": "https://example.com/new-content.html",
      "likes": 42,
      "createdAt": "2025-05-15T12:00:00Z",
      "modifiedAt": "2025-05-15T13:30:00Z",
      "postedBy": "author-id",
      "additionalNotes": "Updated additional notes",
      "content": "Updated content text or HTML",
      "tags": ["technology", "programming", "typescript"]
    }
  }
  ```

### Delete Article
- **Endpoint:** `DELETE /articles/{id}`
- **Handler:** `functions/deleteArticle.handler`
- **Description:** Deletes an article and its associated likes
- **Response:**
  ```json
  {
    "success": true,
    "message": "Article deleted successfully"
  }
  ```

### Get Article Detail
- **Endpoint:** `GET /articles/{id}`
- **Handler:** `functions/getArticle.handler`
- **Description:** Gets detailed information about a specific article
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "id": "article-id",
      "title": "Article Title",
      "description": "Article description text",
      "imageUrl": "https://example.com/image.jpg",
      "contentUrl": "https://example.com/content.html",
      "likes": 42,
      "createdAt": "2025-05-15T12:00:00Z",
      "modifiedAt": "2025-05-15T12:00:00Z",
      "postedBy": "author-id",
      "additionalNotes": "Additional notes about the article",
      "content": "Article content text or HTML",
      "tags": ["technology", "programming"]
    }
  }
  ```

### Like Article
- **Endpoint:** `POST /articles/like`
- **Handler:** `functions/likeArticle.handler`
- **Description:** Likes an article
- **Request:**
  ```json
  {
    "articleId": "article-id",
    "userId": "user-id"
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "message": "Article liked successfully"
  }
  ```

### Unlike Article
- **Endpoint:** `POST /articles/unlike`
- **Handler:** `functions/unlikeArticle.handler`
- **Description:** Unlikes an article
- **Request:**
  ```json
  {
    "articleId": "article-id",
    "userId": "user-id"
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "message": "Article unliked successfully"
  }
  ```

### Check if Article is Liked
- **Endpoint:** `GET /articles/{articleId}/liked`
- **Handler:** `functions/checkArticleLiked.handler`
- **Description:** Checks if the user has liked a specific article
- **Query Parameters:**
    - `userId` (required): User ID to check
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "isLiked": true // or false
    }
  }
  ```

### Get User's Liked Articles
- **Endpoint:** `GET /users/{userId}/liked-articles`
- **Handler:** `functions/getUserLikedArticles.handler`
- **Description:** Gets a list of articles liked by a specific user
- **Query Parameters:**
    - `limit` (optional): Number of articles to retrieve (default: 20)
    - `lastKey` (optional): Key for pagination
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "items": [
        {
          "id": "article-id",
          "title": "Article Title",
          "description": "Article description text",
          "imageUrl": "https://example.com/image.jpg",
          "contentUrl": "https://example.com/content.html",
          "likes": 42,
          "createdAt": "2025-05-15T12:00:00Z",
          "modifiedAt": "2025-05-15T12:00:00Z",
          "postedBy": "author-id",
          "additionalNotes": "Additional notes about the article",
          "content": "Article content text or HTML",
          "tags": ["technology", "programming"]
        }
      ],
      "nextKey": "key-for-next-page"
    }
  }
  ```

### Get Article's Likers
- **Endpoint:** `GET /articles/{articleId}/likers`
- **Handler:** `functions/getArticleLikers.handler`
- **Description:** Gets a list of users who liked a specific article
- **Query Parameters:**
    - `limit` (optional): Number of users to retrieve (default: 20)
    - `lastKey` (optional): Key for pagination
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "items": [
        {
          "userId": "user-id-1",
          "createdAt": "2025-05-15T12:10:00Z"
        },
        {
          "userId": "user-id-2",
          "createdAt": "2025-05-15T13:30:00Z"
        }
      ],
      "nextKey": "key-for-next-page",
      "totalLikes": 42
    }
  }
  ```

All endpoints may return these error responses:

### Bad Request (400)
```json
{
  "success": false,
  "error": "Validation error message"
}
```

### Not Found (404)
```json
{
  "success": false,
  "error": "Article not found"
}
```

### Server Error (500)
```json
{
  "success": false,
  "error": "Internal server error"
}
```