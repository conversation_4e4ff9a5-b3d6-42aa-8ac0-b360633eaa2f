NODE_ENV=staging
STAGE=stage
TABLE_PREFIX=""
BUCKET_NAME=thyview-storage-stage

# API Keys (use environment variables)
TMDB_API_KEY=f96e6f617641625fd2b719874603a237
HUGGING_FACE_TOKEN=*************************************

# Redis Configuration (Staging ElastiCache)
REDIS_HOST=thyview-redis-stage.cache.amazonaws.com
REDIS_PORT=6379
REDIS_PASSWORD=your_staging_redis_password

# DynamoDB Configuration
SEARCH_LOGS_TABLE=search-logs-stage

# AWS Configuration
AWS_REGION=us-east-1

# Logging
LOG_LEVEL=info

# Performance Settings
MEMORY_SIZE=1024
TIMEOUT=30
RESERVED_CONCURRENCY=20
