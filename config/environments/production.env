NODE_ENV=production
STAGE=prod
TABLE_PREFIX=""
BUCKET_NAME=thyview-storage-prod

# API Keys (MUST be set as environment variables)
# TMDB_API_KEY=your_production_tmdb_api_key
# HUGGING_FACE_TOKEN=your_production_hugging_face_token

# Redis Configuration (Production ElastiCache)
REDIS_HOST=thyview-redis-prod.cache.amazonaws.com
REDIS_PORT=6379
# REDIS_PASSWORD=your_production_redis_password

# DynamoDB Configuration
SEARCH_LOGS_TABLE=search-logs-prod

# AWS Configuration
AWS_REGION=us-east-1

# Logging
LOG_LEVEL=error

# Performance Settings
MEMORY_SIZE=1024
TIMEOUT=30
RESERVED_CONCURRENCY=50
