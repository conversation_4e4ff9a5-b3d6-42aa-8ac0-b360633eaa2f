{"name": "thyview-cloud-functions-aws", "version": "1.0.0", "description": "ThyView AWS Lambda Functions", "main": "index.js", "scripts": {"start": "serverless offline", "start:local-server": "cd functions && npm run start:local-server", "deploy": "./scripts/deploy.sh", "deploy:dev": "./scripts/deploy.sh dev", "deploy:stage": "./scripts/deploy.sh stage", "deploy:prod": "./scripts/deploy.sh prod", "logs": "serverless logs --tail", "invoke": "serverless invoke", "remove": "serverless remove", "remove:dev": "serverless remove --stage dev", "remove:prod": "serverless remove --stage prod", "info": "serverless info", "info:dev": "serverless info --stage dev", "info:stage": "serverless info --stage stage", "info:prod": "serverless info --stage prod", "check:stack": "aws cloudformation describe-stacks --region us-east-1", "check:stack:dev": "aws cloudformation describe-stacks --stack-name thyview-api-dev --region us-east-1", "check:stack:stage": "aws cloudformation describe-stacks --stack-name thyview-api-stage --region us-east-1", "check:stack:prod": "aws cloudformation describe-stacks --stack-name thyview-api-prod --region us-east-1"}, "devDependencies": {"@types/flake-idgen": "^0.1.34", "serverless": "^3.40.0", "serverless-dotenv-plugin": "^6.0.0", "serverless-dynamodb-local": "^0.2.40", "serverless-esbuild": "^1.55.0", "serverless-offline": "^13.4.0", "serverless-plugin-typescript": "^2.1.5"}, "private": true}