# Dependencies
node_modules/
.pnp
.pnp.js

# Build outputs
dist/
.webpack/
lib/
coverage/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config/environments/*.local.env

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
.project
.classpath
.c9/
*.launch
.settings/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store
Thumbs.db

# Serverless
.serverless/

# Webpack
.webpack/

# Jest
coverage/
