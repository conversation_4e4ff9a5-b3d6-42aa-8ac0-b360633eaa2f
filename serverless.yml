service: thyview-api

provider:
  name: aws
  runtime: nodejs20.x
  region: ${self:custom.region}
  stage: ${self:custom.stage}
  memorySize: ${self:custom.environments.${self:custom.stage}.memorySize}
  timeout: ${self:custom.environments.${self:custom.stage}.timeout}
  logRetentionInDays: ${self:custom.environments.${self:custom.stage}.logRetentionInDays}

  # Deployment configuration (optional)
  # deploymentBucket:
  #   name: thyview-serverless-deployments-${self:custom.stage}
  #   serverSideEncryption: AES256

  # API Gateway configuration
  apiGateway:
    description: "ThyView API Gateway for ${self:custom.stage} environment"
    minimumCompressionSize: 1024

  # API Gateway naming
  apiName: thyview-api-${self:custom.stage}

  # Stack naming
  stackName: thyview-api-${self:custom.stage}

  environment:
    NODE_ENV: ${self:custom.stage}
    STAGE: ${self:custom.stage}
    TABLE_PREFIX: ""
    BUCKET_NAME: thyview-storage-${self:custom.stage}
    TMDB_API_KEY: ${env:TMDB_API_KEY, "f96e6f617641625fd2b719874603a237"}
    HUGGING_FACE_TOKEN: ${env:HUGGING_FACE_TOKEN, "*************************************"}
    REDIS_URL: "redis://default:<EMAIL>:6379"
    REDIS_HOST: ${self:custom.environments.${self:custom.stage}.redisHost}
    REDIS_PORT: ${self:custom.environments.${self:custom.stage}.redisPort}
    REDIS_PASSWORD: ${env:REDIS_PASSWORD, "your_redis_password"}
    SEARCH_LOGS_TABLE: searchlogs-${self:custom.stage}
    PROFILE_PICTURE_BUCKET: thyview-profile-pictures-${self:custom.stage}

  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - dynamodb:*
          Resource: "*"
        - Effect: Allow
          Action:
            - s3:*
          Resource: "*"
        - Effect: Allow
          Action:
            - logs:*
          Resource: "*"

plugins:
  - serverless-esbuild
  - serverless-offline
  - serverless-dotenv-plugin
  - serverless-dynamodb-local

custom:
  # Stage-specific configurations
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}

  # Environment-specific settings
  environments:
    dev:
      memorySize: 512
      timeout: 30
      logRetentionInDays: 7
      redisHost: "localhost"
      redisPort: "6379"
    stage:
      memorySize: 1024
      timeout: 30
      logRetentionInDays: 14
      redisHost: "thyview-redis-stage.cache.amazonaws.com"
      redisPort: "6379"
    prod:
      memorySize: 1024
      timeout: 30
      logRetentionInDays: 30
      redisHost: "thyview-redis-prod.cache.amazonaws.com"
      redisPort: "6379"

  esbuild:
    bundle: true
    minify: false  # Set to true for production builds
    sourcemap: true
    target: 'node20'
    define:
      'process.env.NODE_ENV': "'${self:provider.environment.NODE_ENV}'"
    platform: 'node'
    concurrency: 10
    exclude:
      - 'aws-sdk'
    external:
      - 'canvas'

  serverless-offline:
    httpPort: 3000
    lambdaPort: 3002
    websocketPort: 3001
    host: 0.0.0.0
    stage: dev
    prefix: v1
    printOutput: true
    
  dynamodb:
    stages:
      - dev
    start:
      port: 8000
      inMemory: true
      migrate: true
      seed: true
      noStart: false
      sharedDb: true
    # Uncomment if you want to use a specific DynamoDB local database directory
    # dbPath: ./.dynamodb
    
    # Seed configuration for local development
    seed:
      posts:
        sources:
          - table: ${self:provider.environment.TABLE_PREFIX}posts
            sources: [./seeds/posts.json]
      post_likes:
        sources:
          - table: ${self:provider.environment.TABLE_PREFIX}post_likes
            sources: [./seeds/post_likes.json]
      comments:
        sources:
          - table: ${self:provider.environment.TABLE_PREFIX}comments
            sources: [./seeds/comments.json]
      comment_likes:
        sources:
          - table: ${self:provider.environment.TABLE_PREFIX}comment_likes
            sources: [./seeds/comment_likes.json]
      users:
        sources:
          - table: ${self:provider.environment.TABLE_PREFIX}users
            sources: [./seeds/users.json]
      articles:
        sources:
          - table: ${self:provider.environment.TABLE_PREFIX}articles
            sources: [./seeds/articles.json]
      article_likes:
        sources:
          - table: ${self:provider.environment.TABLE_PREFIX}article_likes
            sources: [./seeds/article_likes.json]

functions:
  firebaseAuthorizer:
    handler: functions/src/authorizer/firebaseAuthorizer.handler
    name: firebaseAuthorizer-${self:custom.stage}
  # Users
  createUser:
    handler: functions/src/users/createUser.createUser
    name: createUser-${self:custom.stage}
    events:
      - http:
          path: /users
          method: post
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization

  getUserById:
    handler: functions/src/users/getUserById.getUserById
    name: getUserById-${self:custom.stage}
    events:
      - http:
          path: /users/me
          method: get
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization

  getUserByUsername:
    handler: functions/src/users/getUserByUsername.getUserByUsername
    name: getUserByUsername-${self:custom.stage}
    events:
      - http:
          path: /users/by-username
          method: get
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization

  updateUser:
    handler: functions/src/users/updateUser.updateUser
    name: updateUser-${self:custom.stage}
    events:
      - http:
          path: /users/me
          method: put
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization

  deleteUser:
    handler: functions/src/users/deleteUser.deleteUser
    name: deleteUser-${self:custom.stage}
    events:
      - http:
          path: /users/me
          method: delete
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization
  checkUsernameAvailability:
    handler: functions/src/users/checkUsernameAvailability.checkUsernameAvailability
    name: checkUsernameAvailability-${self:custom.stage}
    events:
      - http:
          path: /users/check-username
          method: get
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization
  getPresignedUrl:
    handler: functions/src/users/getPresignedUrl.getPresignedUrl
    name: getPresignedUrl-${self:custom.stage}
    events:
      - http:
          path: /users/upload-url
          method: get
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization
  getProfileImageUrl:
    handler: functions/src/users/getProfileImageUrl.getProfileImageUrl
    name: getProfileImageUrl-${self:custom.stage}
    events:
      - http:
          path: /users/profile-image-url
          method: get
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization
  # Posts (migrated from Firebase)
  createPost:
    handler: functions/src/reviews/posts/createPost.createPost
    name: createPost-${self:custom.stage}
    events:
      - http:
          path: /posts
          method: post
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization

  getPostsList:
    handler: functions/src/reviews/posts/getPostsList.getPostsList
    name: getPostsList-${self:custom.stage}
    events:
      - http:
          path: /posts
          method: get
          cors: true

  editPost:
    handler: functions/src/reviews/posts/editPost.editPost
    name: editPost-${self:custom.stage}
    events:
      - http:
          path: /posts/{postId}
          method: put
          cors: true

  deletePost:
    handler: functions/src/reviews/posts/deletePost.deletePost
    name: deletePost-${self:custom.stage}
    events:
      - http:
          path: /posts/{postId}
          method: delete
          cors: true

  likePost:
    handler: functions/src/reviews/posts/likePost.likePost
    name: likePost-${self:custom.stage}
    events:
      - http:
          path: /posts/{postId}/like
          method: post
          cors: true

  hasUserLiked:
    handler: functions/src/reviews/posts/hasUserLiked.hasUserLiked
    name: hasUserLiked-${self:custom.stage}
    events:
      - http:
          path: /posts/{postId}/hasLiked
          method: get
          cors: true

  getPostDetail:
    handler: functions/src/reviews/posts/getPostDetail.getPostDetail
    name: getPostDetail-${self:custom.stage}
    events:
      - http:
          path: /posts/{postId}
          method: get
          cors: true

  getImageUploadUrl:
    handler: functions/src/reviews/posts/getImageUploadUrl.getImageUploadUrl
    name: getImageUploadUrl-${self:custom.stage}
    events:
      - http:
          path: /posts/uploadUrl
          method: post
          cors: true

  # Comments (migrated from Firebase)
  addComment:
    handler: functions/src/reviews/comments/addComment.addComment
    name: addComment-${self:custom.stage}
    events:
      - http:
          path: /comments
          method: post
          cors: true

  getComments:
    handler: functions/src/reviews/comments/getComment.getComments
    name: getComments-${self:custom.stage}
    events:
      - http:
          path: /posts/{postId}/comments
          method: get
          cors: true

  getReplies:
    handler: functions/src/reviews/comments/getReplies.getReplies
    name: getReplies-${self:custom.stage}
    events:
      - http:
          path: /comments/{commentId}/replies
          method: get
          cors: true

  likeComment:
    handler: functions/src/reviews/comments/likeComment.likeComment
    name: likeComment-${self:custom.stage}
    events:
      - http:
          path: /comments/{commentId}/like
          method: post
          cors: true

  deleteComment:
    handler: functions/src/reviews/comments/deleteComment.deleteComment
    name: deleteComment-${self:custom.stage}
    events:
      - http:
          path: /comments/{commentId}
          method: delete
          cors: true

  submitComment:
    handler: functions/src/reviews/comments/submitComment.submitComment
    name: submitComment-${self:custom.stage}
    events:
      - http:
          path: /posts/{postId}/comments
          method: post
          cors: true

  # Reports (migrated from Firebase)
  reportPost:
    handler: functions/src/reviews/reports/reportPost.reportPost
    name: reportPost-${self:custom.stage}
    events:
      - http:
          path: /posts/{postId}/report
          method: post
          cors: true

  reportComment:
    handler: functions/src/reviews/reports/reportComment.reportComment
    name: reportComment-${self:custom.stage}
    events:
      - http:
          path: /comments/{commentId}/report
          method: post
          cors: true

  # Articles API
  createArticle:
    handler: functions/src/index.createArticle
    name: createArticle-${self:custom.stage}
    events:
      - http:
          path: /articles
          method: post
          cors: true

  getArticles:
    handler: functions/src/index.getArticles
    name: getArticles-${self:custom.stage}
    events:
      - http:
          path: /articles
          method: get
          cors: true

  getArticle:
    handler: functions/src/index.getArticle
    name: getArticle-${self:custom.stage}
    events:
      - http:
          path: /articles/{id}
          method: get
          cors: true

  updateArticle:
    handler: functions/src/index.updateArticle
    name: updateArticle-${self:custom.stage}
    events:
      - http:
          path: /articles/{id}
          method: put
          cors: true

  deleteArticle:
    handler: functions/src/index.deleteArticle
    name: deleteArticle-${self:custom.stage}
    events:
      - http:
          path: /articles/{id}
          method: delete
          cors: true

  likeArticle:
    handler: functions/src/index.likeArticle
    name: likeArticle-${self:custom.stage}
    events:
      - http:
          path: /articles/like
          method: post
          cors: true

  unlikeArticle:
    handler: functions/src/index.unlikeArticle
    name: unlikeArticle-${self:custom.stage}
    events:
      - http:
          path: /articles/unlike
          method: post
          cors: true

  checkArticleLiked:
    handler: functions/src/index.checkArticleLiked
    name: checkArticleLiked-${self:custom.stage}
    events:
      - http:
          path: /articles/{id}/liked
          method: get
          cors: true

  getUserLikedArticles:
    handler: functions/src/index.getUserLikedArticles
    name: getUserLikedArticles-${self:custom.stage}
    events:
      - http:
          path: /users/liked-articles/{userId}
          method: get
          cors: true

  getArticleLikers:
    handler: functions/src/index.getArticleLikers
    name: getArticleLikers-${self:custom.stage}
    events:
      - http:
          path: /articles/{id}/likers
          method: get
          cors: true

  # Movie Search API
  movieSearch:
    handler: functions/src/movie/search/search.handler
    name: movieSearch-${self:custom.stage}
    description: "Search for movies, TV shows, and people using TMDB API with intelligent query classification"
    memorySize: 1024  # Higher memory for ML classification
    timeout: 30
    # reservedConcurrency: 5  # Commented out for initial deployment
    environment:
      FUNCTION_NAME: movieSearch
    events:
      - http:
          path: /movie/search
          method: get
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
            allowCredentials: false
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization
          request:
            parameters:
              querystrings:
                q: true  # Required query parameter

  movieDetails:
    handler: functions/src/movie/details/details.movieDetailsHandler
    name: movieDetails-${self:custom.stage}
    events:
      - http:
          path: /movie/details/movie/{id}
          method: get
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization

  tvDetails:
    handler: functions/src/movie/details/details.tvDetailsHandler
    name: tvDetails-${self:custom.stage}
    events:
      - http:
          path: /movie/details/tv/{id}
          method: get
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization

  personDetails:
    handler: functions/src/movie/details/details.personDetailsHandler
    name: personDetails-${self:custom.stage}
    events:
      - http:
          path: /movie/details/person/{id}
          method: get
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization

  movieTrending:
    handler: functions/src/movie/trending/trending.handler
    name: movieTrending-${self:custom.stage}
    events:
      - http:
          path: /movie/trending
          method: get
          cors: true
          authorizer:
            name: firebaseAuthorizer
            type: token
            identitySource: method.request.header.Authorization

resources:
  Resources:
    # DynamoDB Tables
    PostsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: posts-${self:custom.stage}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: authorUid
            AttributeType: S
          - AttributeName: createdAt
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        GlobalSecondaryIndexes:
          - IndexName: AuthorCreatedAtIndex
            KeySchema:
              - AttributeName: authorUid
                KeyType: HASH
              - AttributeName: createdAt
                KeyType: RANGE
            Projection:
              ProjectionType: ALL

    PostLikesTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: postlikes-${self:custom.stage}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: postId
            AttributeType: S
          - AttributeName: userId
            AttributeType: S
        KeySchema:
          - AttributeName: postId
            KeyType: HASH
          - AttributeName: userId
            KeyType: RANGE

    CommentsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: comments-${self:custom.stage}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: postId
            AttributeType: S
          - AttributeName: parentCommentId
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        GlobalSecondaryIndexes:
          - IndexName: PostIdParentCommentIndex
            KeySchema:
              - AttributeName: postId
                KeyType: HASH
              - AttributeName: parentCommentId
                KeyType: RANGE
            Projection:
              ProjectionType: ALL

    CommentLikesTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: commentlikes-${self:custom.stage}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: commentId
            AttributeType: S
          - AttributeName: userId
            AttributeType: S
        KeySchema:
          - AttributeName: commentId
            KeyType: HASH
          - AttributeName: userId
            KeyType: RANGE

    PostReportsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: postreports-${self:custom.stage}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH

    CommentReportsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: commentreports-${self:custom.stage}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH

    UsersTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: users-${self:custom.stage}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: username
            AttributeType: S
          - AttributeName: email
            AttributeType: S
          - AttributeName: externalUserId
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        GlobalSecondaryIndexes:
          - IndexName: username-index
            KeySchema:
              - AttributeName: username
                KeyType: HASH
            Projection:
              ProjectionType: ALL
          - IndexName: email-index
            KeySchema:
              - AttributeName: email
                KeyType: HASH
            Projection:
              ProjectionType: ALL
          - IndexName: externalUserId-index
            KeySchema:
              - AttributeName: externalUserId
                KeyType: HASH
            Projection:
              ProjectionType: ALL


    # Articles Tables
    ArticlesTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: articles-${self:custom.stage}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: title
            AttributeType: S
          - AttributeName: likes
            AttributeType: N
          - AttributeName: createdAt
            AttributeType: S
          - AttributeName: postedBy
            AttributeType: S
          - AttributeName: tags
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        GlobalSecondaryIndexes:
          - IndexName: title-index
            KeySchema:
              - AttributeName: title
                KeyType: HASH
            Projection:
              ProjectionType: ALL
          - IndexName: likes-index
            KeySchema:
              - AttributeName: likes
                KeyType: HASH
              - AttributeName: createdAt
                KeyType: RANGE
            Projection:
              ProjectionType: ALL
          - IndexName: createdAt-index
            KeySchema:
              - AttributeName: createdAt
                KeyType: HASH
            Projection:
              ProjectionType: ALL
          - IndexName: postedBy-index
            KeySchema:
              - AttributeName: postedBy
                KeyType: HASH
              - AttributeName: createdAt
                KeyType: RANGE
            Projection:
              ProjectionType: ALL
          - IndexName: tags-index
            KeySchema:
              - AttributeName: tags
                KeyType: HASH
            Projection:
              ProjectionType: ALL

    ArticleLikesTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: articlelikes-${self:custom.stage}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: articleId
            AttributeType: S
          - AttributeName: userId
            AttributeType: S
          - AttributeName: createdAt
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
        GlobalSecondaryIndexes:
          - IndexName: articleId-index
            KeySchema:
              - AttributeName: articleId
                KeyType: HASH
              - AttributeName: createdAt
                KeyType: RANGE
            Projection:
              ProjectionType: ALL
          - IndexName: userId-index
            KeySchema:
              - AttributeName: userId
                KeyType: HASH
              - AttributeName: createdAt
                KeyType: RANGE
            Projection:
              ProjectionType: ALL

    StorageBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:provider.environment.BUCKET_NAME}
        CorsConfiguration:
          CorsRules:
            - AllowedHeaders:
                - '*'
              AllowedMethods:
                - GET
                - PUT
                - POST
                - DELETE
                - HEAD
              AllowedOrigins:
                - '*'
              MaxAge: 3000

    ProfilePicturesBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: thyview-profile-pictures-${self:custom.stage}
        PublicAccessBlockConfiguration:
          BlockPublicAcls: true
          BlockPublicPolicy: true
          IgnorePublicAcls: true
          RestrictPublicBuckets: true
        CorsConfiguration:
          CorsRules:
            - AllowedHeaders:
                - '*'
              AllowedMethods:
                - PUT
                - GET
              AllowedOrigins:
                - '*'
              ExposedHeaders:
                - ETag
