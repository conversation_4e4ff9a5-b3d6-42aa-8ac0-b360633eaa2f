FROM node:23-alpine

WORKDIR /app

# Install serverless globally
RUN npm install -g serverless

# Copy package.json and install dependencies
COPY functions/package.json functions/package-lock.json* ./functions/
RUN cd functions && npm install

# Copy project files
COPY . .

# Set environment to development
ENV NODE_ENV=development

# Expose port for serverless offline
EXPOSE 3000

# Command to run local development server
CMD ["npm", "run", "start", "--prefix", "functions"]
