#!/bin/bash

echo "Verifying project setup..."

# Check if required directories exist
for dir in "functions/src/common" "functions/tests" "config/environments" "scripts"; do
  if [ ! -d "$dir" ]; then
    echo "❌ Directory $dir is missing"
  else
    echo "✅ Directory $dir exists"
  fi
done

# Check if key files exist
for file in "functions/package.json" "functions/tsconfig.json" "functions/.eslintrc.json" "README.md" "serverless.yml"; do
  if [ ! -f "$file" ]; then
    echo "❌ File $file is missing"
  else
    echo "✅ File $file exists"
  fi
done

# Check Node.js version
node_version=$(node -v)
echo "Node.js version: $node_version"
if [[ $node_version != v20* ]]; then
  echo "⚠️ Node.js version should be 20.x for production use"
else
  echo "✅ Node.js version is 20.x"
fi

# Check npm dependencies
echo "Checking for outdated npm packages..."
cd functions && npm outdated

echo "Setup verification complete!"
