const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

// Read the current serverless.yml
const serverlessPath = path.join(__dirname, '..', 'serverless.yml');
const serverlessContent = fs.readFileSync(serverlessPath, 'utf8');
const config = yaml.load(serverlessContent);

// Update the configuration
config.provider = config.provider || {};
config.provider.logRetentionInDays = 30; // Increase for production
config.provider.tracing = { lambda: true }; // Add X-Ray tracing for production monitoring

// Add better environment variables handling
config.provider.environment = {
  ...config.provider.environment,
  STAGE: '${opt:stage, self:provider.stage}',
  REGION: '${self:provider.region}'
};

// Add custom domain if not present
config.custom = config.custom || {};
if (!config.custom.customDomain) {
  config.custom.customDomain = {
    domainName: '${self:custom.domains.${opt:stage, self:provider.stage}}',
    basePath: '',
    stage: '${opt:stage, self:provider.stage}',
    createRoute53Record: true
  };
  config.custom.domains = {
    dev: 'api-dev.thyview.com',
    stage: 'api-stage.thyview.com',
    prod: 'api.thyview.com'
  };
}

// Add better webpack configuration
config.custom.esbuild = {
  ...config.custom.esbuild,
  packager: 'npm',
  excludeFiles: '**/*.test.ts'
};

// Write the modified serverless.yml
fs.writeFileSync(serverlessPath, yaml.dump(config, { lineWidth: 120 }));
console.log('Updated serverless.yml with production-ready configurations');
