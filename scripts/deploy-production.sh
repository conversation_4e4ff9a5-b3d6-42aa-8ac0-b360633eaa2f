#!/bin/bash

set -e

echo "Starting production deployment process..."

# Check if we're on the production branch
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [ "$CURRENT_BRANCH" != "production" ]; then
  echo "❌ Not on production branch. Please switch to the production branch before deploying."
  exit 1
fi

# Check if there are uncommitted changes
if [ -n "$(git status --porcelain)" ]; then
  echo "❌ There are uncommitted changes. Please commit or stash them before deploying."
  exit 1
fi

# Run tests
echo "Running tests..."
cd functions && npm test
if [ $? -ne 0 ]; then
  echo "❌ Tests failed. Aborting deployment."
  exit 1
fi

# Build the project
echo "Building the project..."
cd functions && npm run build
if [ $? -ne 0 ]; then
  echo "❌ Build failed. Aborting deployment."
  exit 1
fi

# Deploy to production
echo "Deploying to production..."
cd functions && npm run deploy:prod
if [ $? -ne 0 ]; then
  echo "❌ Deployment failed."
  exit 1
fi

echo "✅ Production deployment completed successfully!"
