const fs = require('fs');

// Function to check if a module is installed
function isModuleInstalled(moduleName) {
  try {
    require.resolve(moduleName);
    return true;
  } catch (e) {
    return false;
  }
}

// Apply patch to serverless-esbuild if it exists
if (isModuleInstalled('serverless-esbuild')) {
  try {
    const esbuildHelperPath = require.resolve('serverless-esbuild/dist/helper');
    const content = fs.readFileSync(esbuildHelperPath, 'utf8');
    
    // Skip symlink creation by replacing the function
    const patchedContent = content.replace(
      /function symlinkNodeModules\(([^)]+)\)\s*{[\s\S]+?return\s+[^;]+;}/,
      'function symlinkNodeModules($1) { console.log("Skipping node_modules symlink"); return Promise.resolve(); }'
    );
    
    if (content !== patchedContent) {
      fs.writeFileSync(esbuildHelperPath, patchedContent);
      console.log('✅ Successfully patched serverless-esbuild to avoid symlink error');
    } else {
      console.log('ℹ️ Patch already applied or pattern not found');
    }
  } catch (err) {
    console.error('❌ Failed to patch serverless-esbuild:', err);
  }
} else {
  console.log('⚠️ serverless-esbuild not found, skipping patch');
}
